package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.ChatMessage
import biz.zhizuo.ai.assistant.entity.MessageRole
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * 聊天消息数据访问接口
 */
@Repository
interface ChatMessageRepository : JpaRepository<ChatMessage, String> {

    /**
     * 根据会话ID查找所有消息，按创建时间正序排列
     */
    fun findBySessionIdOrderByCreatedAtAsc(sessionId: String): List<ChatMessage>

    /**
     * 根据会话ID和用户ID查找所有消息
     */
    fun findBySessionIdAndUserIdOrderByCreatedAtAsc(sessionId: String, userId: String): List<ChatMessage>

    /**
     * 根据消息ID和用户ID查找消息
     */
    fun findByIdAndUserId(id: String, userId: String): ChatMessage?

    /**
     * 根据回复目标ID查找所有回复消息
     */
    fun findByReplyToIdOrderByCreatedAtAsc(replyToId: String): List<ChatMessage>

    /**
     * 根据会话ID统计消息数量
     */
    fun countBySessionId(sessionId: String): Long

    /**
     * 根据会话ID和角色统计消息数量
     */
    fun countBySessionIdAndRole(sessionId: String, role: MessageRole): Long

    /**
     * 删除会话的所有消息
     */
    fun deleteBySessionId(sessionId: String)

    /**
     * 删除用户的所有消息
     */
    fun deleteByUserId(userId: String)

    /**
     * 查找正在生成的消息
     */
    fun findByIsGeneratingTrue(): List<ChatMessage>

    /**
     * 查找用户正在生成的消息
     */
    fun findByUserIdAndIsGeneratingTrue(userId: String): List<ChatMessage>
}
