:host {
  display: block;
}

// 用户菜单容器
.user-menu-container {
  display: inline-block;
  margin-right: var(--md-sys-spacing-1);
  position: relative;

  .user-avatar-button {
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    position: relative;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: scale(0);
      transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.25);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    }

    &:hover::after {
      transform: scale(1);
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.3);
    }

    mat-icon {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
  }

  .menu-trigger-hidden {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    pointer-events: none;
  }
}

// 菜单内容样式
.menu-content {
  min-width: 220px;
  background-color: #ffffff;
  overflow: hidden;
}

// 菜单头部
.menu-header {
  padding: var(--md-sys-spacing-6) var(--md-sys-spacing-6) var(--md-sys-spacing-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(37, 99, 235, 0.05);

  // 用户头像
  .user-avatar, .guest-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: var(--md-sys-color-primary-container);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--md-sys-spacing-4);
    box-shadow: var(--md-sys-elevation-level2);
    border: 3px solid rgba(255, 255, 255, 0.9);
    transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard), box-shadow var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      transform: scale(1.05);
      box-shadow: var(--md-sys-elevation-level3);
    }

    mat-icon {
      color: white;
      font-size: 40px;
      width: 40px;
      height: 40px;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  // 访客头像特殊样式
  .guest-avatar {
    background-color: #6b7280;
  }

  // 用户名和访客标签
  .user-name, .guest-label {
    font-weight: var(--md-sys-typescale-font-weight-semibold);
    color: var(--md-sys-color-on-surface);
    font-size: var(--md-sys-typescale-title-medium-size);
    margin-top: var(--md-sys-spacing-1);
    margin-bottom: 0; // 确保底部没有外边距
  }

  .guest-label {
    color: #6b7280;
  }
}

// 菜单项容器
.menu-items {
  overflow: hidden;

  // 菜单项
  .menu-item {
    border-radius: 0; // 移除圆角
    margin: 0; // 移除外边距
    height: 48px;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    width: 100%; // 使用完整宽度

    padding: 0 var(--md-sys-spacing-6);

    &:hover {
      background-color: rgba(37, 99, 235, 0.08);
      transform: translateX(0);
    }

    &:active {
      background-color: rgba(37, 99, 235, 0.12);
    }

    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;

    mat-icon {
      display: inline-flex;
      color: #4b5563;
    }

    span {
      font-weight: var(--md-sys-typescale-font-weight-medium);
      font-size: var(--md-sys-typescale-body-medium-size);
      color: var(--md-sys-color-on-surface);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      height: 100%;
      flex: 1; // 让文本占据剩余空间
    }

    // 子菜单箭头样式
    .submenu-arrow {
      margin-left: auto !important;
      margin-right: 0 !important;
      color: #9ca3af !important;
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }
  }
}

// 自定义用户菜单样式
::ng-deep .user-menu {
  margin-top: var(--md-sys-spacing-1);

  .mat-mdc-menu-panel {
    max-width: none !important;
    overflow-x: hidden !important;
  }

  .mat-mdc-menu-content {
    padding: 0 !important; // 强制移除内边距
  }

  .mat-mdc-menu-item {
    margin: 0 !important; // 强制移除外边距
    border-radius: 0 !important; // 强制移除圆角
  }

  // 确保菜单中的分隔线使用正确的颜色
  .mat-divider {
    border-top-color: var(--md-sys-color-outline) !important;
  }
}

// 帮助子菜单样式
::ng-deep .help-submenu {
  margin-top: var(--md-sys-spacing-1);

  .mat-mdc-menu-panel {
    max-width: none !important;
    overflow-x: hidden !important;
  }

  .mat-mdc-menu-content {
    padding: 0 !important;
  }

  .mat-mdc-menu-item {
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .menu-item {
    border-radius: 0;

    height: 48px;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    padding: 0 var(--md-sys-spacing-6);

    &:hover {
      background-color: rgba(37, 99, 235, 0.08);
      transform: translateX(0);
    }

    &:active {
      background-color: rgba(37, 99, 235, 0.12);
    }

    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;

    mat-icon {
      display: inline-flex;
      color: #4b5563;
      margin-right: var(--md-sys-spacing-6);
    }

    span {
      font-weight: var(--md-sys-typescale-font-weight-medium);
      font-size: var(--md-sys-typescale-body-medium-size);
      color: var(--md-sys-color-on-surface);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  // 确保子菜单中的分隔线使用正确的颜色
  .mat-divider {
    border-top-color: var(--md-sys-color-outline) !important;
  }
}
