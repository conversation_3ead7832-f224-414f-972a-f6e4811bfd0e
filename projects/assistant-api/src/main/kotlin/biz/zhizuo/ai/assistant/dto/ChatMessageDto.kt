package biz.zhizuo.ai.assistant.dto

import biz.zhizuo.ai.assistant.entity.AttachmentType
import biz.zhizuo.ai.assistant.entity.MessageRole
import biz.zhizuo.ai.assistant.entity.StepStatus
import java.time.LocalDateTime

/**
 * 消息 DTO
 */
data class ChatMessageDto(
    val id: String?,
    val sessionId: String,
    val role: MessageRole,
    val content: String,
    val isGenerating: Boolean = false,
    val replyToId: String? = null,
    val activeReplyId: String? = null,
    val assistantName: String? = null,
    val liked: Boolean? = null,
    val disliked: Boolean? = null,
    val createdAt: LocalDateTime?,
    val steps: List<ChatMessageGenerationStepDto> = emptyList(),
    val attachments: List<AttachmentDto> = emptyList(),
    val feedback: ChatMessageFeedbackDto? = null,
    val replyIds: List<String> = emptyList()
)

/**
 * 消息生成步骤 DTO
 */
data class ChatMessageGenerationStepDto(
    val id: String?,
    val step: String,
    val description: String? = null,
    val stepOrder: Int,
    val status: StepStatus
)

/**
 * 附件 DTO
 */
data class AttachmentDto(
    val id: String?,
    val name: String,
    val type: AttachmentType,
    val fileSize: Long? = null,
    val mimeType: String? = null,
    val content: String? = null,
    val createdAt: LocalDateTime?
)

/**
 * 消息反馈 DTO
 */
data class ChatMessageFeedbackDto(
    val id: String?,
    val rate: Int,
    val content: String? = null,
    val createdAt: LocalDateTime?
)

/**
 * 创建用户消息请求 DTO
 */
data class CreateUserMessageRequest(
    val sessionId: String,
    val content: String,
    val attachments: List<AttachmentDto>? = null,
    val replyToId: String? = null
)

/**
 * 创建用户消息响应 DTO
 */
data class CreateUserMessageResponse(
    val userMessage: ChatMessageDto,
    val assistantMessage: ChatMessageDto
)

/**
 * 更新消息请求 DTO
 */
data class UpdateMessageRequest(
    val content: String? = null,
    val liked: Boolean? = null,
    val disliked: Boolean? = null,
    val feedback: ChatMessageFeedbackDto? = null,
    val activeReplyId: String? = null,
    val steps: List<ChatMessageGenerationStepDto>? = null
)

/**
 * 重新生成消息请求 DTO
 */
data class RegenerateMessageRequest(
    val messageId: String,
    val sessionId: String
)

/**
 * 重新生成消息响应 DTO
 */
data class RegenerateMessageResponse(
    val assistantMessage: ChatMessageDto
)
