package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.*
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * 执行计划 Repository
 */
@Repository
interface ExecutionPlanRepository : JpaRepository<ExecutionPlan, String> {

    /**
     * 查找活跃的执行计划，按优先级排序
     */
    fun findByIsActiveTrueOrderByPriorityOrderAsc(): List<ExecutionPlan>

    /**
     * 根据领域查找执行计划
     */
    fun findByDomainAndIsActiveTrueOrderByPriorityOrderAsc(domain: String): List<ExecutionPlan>

    /**
     * 向量相似度搜索（暂时禁用，需要数据库支持pgvector扩展）
     */
    // @Query(
    //     value = """
    //     SELECT * FROM execution_plans
    //     WHERE is_active = true
    //     ORDER BY vector_embedding <-> CAST(:queryVector AS vector)
    //     LIMIT :limit
    //     """,
    //     nativeQuery = true
    // )
    // fun findSimilarPlans(
    //     @Param("queryVector") queryVector: String,
    //     @Param("limit") limit: Int = 5
    // ): List<ExecutionPlan>
}

/**
 * 执行计划步骤 Repository
 */
@Repository
interface ExecutionPlanStepRepository : JpaRepository<ExecutionPlanStep, String> {

    /**
     * 根据执行计划ID查找步骤，按顺序排序
     */
    fun findByExecutionPlanIdOrderByStepOrderAsc(executionPlanId: String): List<ExecutionPlanStep>

    /**
     * 根据执行计划查找步骤，按顺序排序
     */
    fun findByExecutionPlanOrderByStepOrderAsc(executionPlan: ExecutionPlan): List<ExecutionPlanStep>
}

/**
 * 消息执行上下文 Repository
 */
@Repository
interface MessageExecutionContextRepository : JpaRepository<MessageExecutionContext, String> {

    /**
     * 根据消息ID查找执行上下文
     */
    fun findByMessageId(messageId: String): MessageExecutionContext?

    /**
     * 根据消息查找执行上下文
     */
    fun findByMessage(message: ChatMessage): MessageExecutionContext?

    /**
     * 查找正在执行的上下文
     */
    fun findByStatusIn(statuses: List<ExecutionStatus>): List<MessageExecutionContext>

    /**
     * 根据用户ID查找执行上下文
     */
    @Query("SELECT mec FROM MessageExecutionContext mec WHERE mec.message.user.id = :userId")
    fun findByUserId(@Param("userId") userId: String): List<MessageExecutionContext>
}

/**
 * 步骤执行记录 Repository
 */
@Repository
interface StepExecutionRepository : JpaRepository<StepExecution, String> {

    /**
     * 根据执行上下文查找步骤执行记录
     */
    fun findByExecutionContextOrderByExecutionPlanStepStepOrderAsc(
        executionContext: MessageExecutionContext
    ): List<StepExecution>

    /**
     * 根据执行上下文ID查找步骤执行记录
     */
    fun findByExecutionContextIdOrderByExecutionPlanStepStepOrderAsc(
        executionContextId: String
    ): List<StepExecution>

    /**
     * 查找正在执行的步骤
     */
    fun findByStatusIn(statuses: List<StepStatus>): List<StepExecution>

    /**
     * 根据执行上下文和步骤顺序查找执行记录
     */
    fun findByExecutionContextAndExecutionPlanStepStepOrder(
        executionContext: MessageExecutionContext,
        stepOrder: Int
    ): StepExecution?
}
