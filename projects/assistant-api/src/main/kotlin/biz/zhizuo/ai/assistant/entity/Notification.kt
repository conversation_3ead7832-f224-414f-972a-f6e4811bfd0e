package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 消息实体
 */
@Entity
@Table(name = "notifications")
@EntityListeners(AuditingEntityListener::class)
data class Notification(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(name = "user_id")
    val userId: String? = null,

    @Column(nullable = false, length = 50)
    val type: String, // 'notification' 或 'private'

    @Column(length = 50)
    val category: String? = null,

    @Column(nullable = false, length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @Column(name = "is_read")
    val isRead: Boolean = false,

    @Column(length = 50)
    val icon: String? = null,

    @Column(length = 50)
    val color: String? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
