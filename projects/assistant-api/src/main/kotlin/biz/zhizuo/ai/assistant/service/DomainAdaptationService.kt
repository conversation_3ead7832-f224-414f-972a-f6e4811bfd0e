package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.*
import org.slf4j.LoggerFactory
import org.springframework.ai.embedding.EmbeddingModel
import org.springframework.ai.embedding.EmbeddingRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 领域适配服务
 * 负责分析用户消息，确定问题领域，并匹配合适的执行计划
 */
@Service
@Transactional
class DomainAdaptationService(
    private val executionPlanRepository: ExecutionPlanRepository,
    private val messageExecutionContextRepository: MessageExecutionContextRepository,
    private val embeddingModel: EmbeddingModel
) {
    
    private val logger = LoggerFactory.getLogger(DomainAdaptationService::class.java)
    
    /**
     * 为用户消息进行领域适配，选择合适的执行计划
     */
    fun adaptDomain(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): DomainAdaptationResultDto {
        logger.info("开始为消息 ${userMessage.id} 进行领域适配")
        
        // 1. 分析消息内容和会话历史，确定问题领域
        val detectedDomain = detectDomain(userMessage, sessionHistory)
        logger.info("检测到问题领域: $detectedDomain")
        
        // 2. 生成查询向量
        val queryVector = generateQueryVector(userMessage, sessionHistory)
        
        // 3. 通过向量匹配找到最合适的执行计划
        val matchedPlan = findBestExecutionPlan(queryVector, detectedDomain)
        
        // 4. 计算匹配置信度
        val confidenceScore = calculateConfidenceScore(userMessage, matchedPlan, detectedDomain)
        
        logger.info("选择执行计划: ${matchedPlan.name}, 置信度: $confidenceScore")
        
        return DomainAdaptationResultDto(
            detectedDomain = detectedDomain,
            confidenceScore = confidenceScore,
            executionPlan = matchedPlan.toDto(),
            reasoning = generateReasoningExplanation(userMessage, matchedPlan, detectedDomain)
        )
    }
    
    /**
     * 创建消息执行上下文
     */
    fun createExecutionContext(
        userMessage: ChatMessage,
        adaptationResult: DomainAdaptationResultDto
    ): MessageExecutionContextDto {
        val executionPlan = executionPlanRepository.findById(adaptationResult.executionPlan.id!!)
            .orElseThrow { IllegalArgumentException("执行计划不存在: ${adaptationResult.executionPlan.id}") }
        
        val executionContext = MessageExecutionContext(
            message = userMessage,
            executionPlan = executionPlan,
            detectedDomain = adaptationResult.detectedDomain,
            confidenceScore = adaptationResult.confidenceScore,
            status = ExecutionStatus.PENDING,
            currentStepOrder = 0
        )
        
        val savedContext = messageExecutionContextRepository.save(executionContext)
        logger.info("创建执行上下文: ${savedContext.id}")
        
        return savedContext.toDto()
    }
    
    /**
     * 检测问题领域
     */
    private fun detectDomain(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): String {
        val content = userMessage.content.lowercase()
        
        // 简单的关键词匹配逻辑，实际应用中可以使用更复杂的NLP模型
        return when {
            content.contains("代码") || content.contains("编程") || content.contains("bug") || 
            content.contains("技术") || content.contains("开发") -> "技术问题"
            
            content.contains("学习") || content.contains("教育") || content.contains("课程") || 
            content.contains("知识") || content.contains("研究") -> "学术研究"
            
            content.contains("生活") || content.contains("日常") || content.contains("建议") || 
            content.contains("推荐") || content.contains("怎么办") -> "生活咨询"
            
            content.contains("创作") || content.contains("写作") || content.contains("文章") || 
            content.contains("故事") || content.contains("创意") -> "创意写作"
            
            content.contains("分析") || content.contains("数据") || content.contains("报告") || 
            content.contains("统计") || content.contains("图表") -> "数据分析"
            
            else -> "通用问答"
        }
    }
    
    /**
     * 生成查询向量
     */
    private fun generateQueryVector(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): String {
        // 构建查询文本：当前消息 + 最近的会话历史
        val queryText = buildString {
            append(userMessage.content)
            
            // 添加最近3条历史消息作为上下文
            sessionHistory.takeLast(3).forEach { msg ->
                append(" ")
                append(msg.content)
            }
        }
        
        // 使用嵌入模型生成向量
        val embeddingRequest = EmbeddingRequest(listOf(queryText), null)
        val embeddingResponse = embeddingModel.call(embeddingRequest)
        
        // 将向量转换为字符串格式（PostgreSQL pgvector格式）
        val vector = embeddingResponse.results.first().output
        return "[${vector.joinToString(",")}]"
    }
    
    /**
     * 找到最佳执行计划
     */
    private fun findBestExecutionPlan(queryVector: String, detectedDomain: String): ExecutionPlan {
        // 首先尝试向量相似度搜索
        val similarPlans = try {
            executionPlanRepository.findSimilarPlans(queryVector, 3)
        } catch (e: Exception) {
            logger.warn("向量搜索失败，使用领域匹配: ${e.message}")
            emptyList()
        }
        
        // 如果向量搜索有结果，选择第一个
        if (similarPlans.isNotEmpty()) {
            return similarPlans.first()
        }
        
        // 否则根据领域匹配
        val domainPlans = executionPlanRepository.findByDomainAndIsActiveTrueOrderByPriorityOrderAsc(detectedDomain)
        if (domainPlans.isNotEmpty()) {
            return domainPlans.first()
        }
        
        // 最后使用默认的通用执行计划
        val defaultPlans = executionPlanRepository.findByIsActiveTrueOrderByPriorityOrderAsc()
        return defaultPlans.firstOrNull() 
            ?: throw IllegalStateException("没有可用的执行计划")
    }
    
    /**
     * 计算匹配置信度
     */
    private fun calculateConfidenceScore(
        userMessage: ChatMessage, 
        executionPlan: ExecutionPlan, 
        detectedDomain: String
    ): Double {
        var score = 0.5 // 基础分数
        
        // 如果领域完全匹配，增加置信度
        if (executionPlan.domain == detectedDomain) {
            score += 0.3
        }
        
        // 根据消息长度调整置信度
        when (userMessage.content.length) {
            in 0..20 -> score -= 0.1 // 太短的消息置信度较低
            in 21..100 -> score += 0.1 // 适中长度
            else -> score += 0.2 // 较长的消息通常包含更多信息
        }
        
        return score.coerceIn(0.0, 1.0)
    }
    
    /**
     * 生成推理解释
     */
    private fun generateReasoningExplanation(
        userMessage: ChatMessage,
        executionPlan: ExecutionPlan,
        detectedDomain: String
    ): String {
        return "基于消息内容分析，检测到问题属于「$detectedDomain」领域，" +
                "选择执行计划「${executionPlan.name}」来处理此类问题。"
    }
    
    /**
     * 实体转DTO
     */
    private fun ExecutionPlan.toDto(): ExecutionPlanDto {
        return ExecutionPlanDto(
            id = this.id,
            name = this.name,
            description = this.description,
            domain = this.domain,
            isActive = this.isActive,
            priorityOrder = this.priorityOrder,
            steps = this.steps.map { it.toDto() }
        )
    }
    
    private fun ExecutionPlanStep.toDto(): ExecutionPlanStepDto {
        return ExecutionPlanStepDto(
            id = this.id,
            name = this.name,
            description = this.description,
            stepOrder = this.stepOrder,
            modelName = this.modelName,
            expectedDurationSeconds = this.expectedDurationSeconds,
            isParallel = this.isParallel
        )
    }
    
    private fun MessageExecutionContext.toDto(): MessageExecutionContextDto {
        return MessageExecutionContextDto(
            id = this.id,
            messageId = this.message.id!!,
            executionPlan = this.executionPlan.toDto(),
            detectedDomain = this.detectedDomain,
            confidenceScore = this.confidenceScore,
            status = this.status,
            currentStepOrder = this.currentStepOrder,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            stepExecutions = this.stepExecutions.map { it.toDto() }
        )
    }
    
    private fun StepExecution.toDto(): StepExecutionDto {
        return StepExecutionDto(
            id = this.id,
            executionPlanStep = this.executionPlanStep.toDto(),
            status = this.status,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            durationSeconds = this.durationSeconds,
            output = this.output,
            errorMessage = this.errorMessage
        )
    }
}
