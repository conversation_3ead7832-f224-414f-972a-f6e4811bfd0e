package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.security.UserPrincipal
import biz.zhizuo.ai.assistant.service.ChatMessageService
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 * 聊天消息控制器
 */
@RestController
@RequestMapping("/api/messages")
class ChatMessageController(
    private val chatMessageService: ChatMessageService,
) {

    /**
     * 获取会话中的所有消息
     */
    @GetMapping
    fun getMessages(
        @RequestParam sessionId: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<List<ChatMessageDto>> {
        val messages = chatMessageService.getSessionMessages(sessionId, userPrincipal.id)
        return ResponseEntity.ok(messages)
    }

    /**
     * 获取单个消息
     */
    @GetMapping("/{id}")
    fun getMessage(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<ChatMessageDto> {
        val message = chatMessageService.getMessageDtoById(id, userPrincipal.id)
        return ResponseEntity.ok(message)
    }

    /**
     * 创建用户消息
     */
    @PostMapping("/user")
    fun createUserMessage(
        @RequestBody request: CreateUserMessageRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<CreateUserMessageResponse> {
        val response = chatMessageService.createUserMessage(request, userPrincipal.id)
        // 异步生成回复的逻辑已移至 ChatMessageService
        return ResponseEntity.status(HttpStatus.CREATED).body(response)
    }

    /**
     * 更新消息
     */
    @PatchMapping("/{id}")
    fun updateMessage(
        @PathVariable id: String,
        @RequestBody request: UpdateMessageRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<ChatMessageDto> {
        val message = chatMessageService.updateMessage(id, request, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(message)
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{id}")
    fun deleteMessage(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<Void> {
        val deleted = chatMessageService.deleteMessage(id, userPrincipal.id)
        if (!deleted) {
            return ResponseEntity.notFound().build()
        }
        return ResponseEntity.noContent().build()
    }

    /**
     * 开始助理消息生成
     */
    @PostMapping("/{id}/generate")
    fun startGeneration(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<Map<String, String>> {
        val result = chatMessageService.startGeneration(id, userPrincipal.id)
        return ResponseEntity.ok(result)
    }

    /**
     * 点赞消息
     */
    @PostMapping("/{id}/like")
    fun likeMessage(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<ChatMessageDto> {
        val message = chatMessageService.likeMessage(id, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(message)
    }

    /**
     * 点踩消息
     */
    @PostMapping("/{id}/dislike")
    fun dislikeMessage(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<ChatMessageDto> {
        val message = chatMessageService.dislikeMessage(id, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(message)
    }

    /**
     * 添加消息反馈
     */
    @PostMapping("/{id}/feedback")
    fun addFeedback(
        @PathVariable id: String,
        @RequestBody feedback: ChatMessageFeedbackDto,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<ChatMessageDto> {
        val message = chatMessageService.addMessageFeedback(id, feedback, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(message)
    }

    /**
     * 重新生成消息
     */
    @PostMapping("/regenerate")
    fun regenerateMessage(
        @RequestBody request: RegenerateMessageRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): ResponseEntity<RegenerateMessageResponse> {
        val response = chatMessageService.regenerateMessage(request, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        // 异步生成回复的逻辑已移至 ChatMessageService
        return ResponseEntity.ok(response)
    }

    /**
     * 获取消息事件流 (SSE)
     */
    @GetMapping("/{id}/events", produces = [MediaType.TEXT_EVENT_STREAM_VALUE])
    fun getMessageEventStream(
        @PathVariable id: String,
        @RequestParam token: String, // token 用于鉴权，此处暂不实现具体逻辑
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
    ): SseEmitter {
        val emitter = SseEmitter(Long.MAX_VALUE) // Timeout set to a very long period
        val key = "${userPrincipal.id}:$id"

        chatMessageService.registerSseEmitter(key, emitter)
        // emitter 的生命周期管理（onCompletion, onTimeout, onError）已在 ChatMessageService 中处理

        return emitter
    }
}
