package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.UserProfile
import biz.zhizuo.ai.assistant.repository.UserProfileRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 用户资料服务
 */
@Service
@Transactional
class ProfileService(
    private val userProfileRepository: UserProfileRepository
) {

    /**
     * 获取用户资料
     */
    fun getProfile(userId: String): ProfileResponse {
        val profile = userProfileRepository.findByUserId(userId)
            .orElseThrow { UserProfileNotFoundException(userId) }

        val profileDto = profile.toDtoInternal()

        // 模拟活动数据
        val activities = listOf(
            ActivityDto(
                id = "1",
                type = "post",
                title = "发布了新文章",
                description = "《如何使用 Spring Boot 构建 REST API》",
                timestamp = "2024-01-15T10:30:00Z",
                icon = "edit",
                color = "primary"
            ),
            ActivityDto(
                id = "2",
                type = "comment",
                title = "评论了文章",
                description = "对《微服务架构设计》发表了评论",
                timestamp = "2024-01-14T15:20:00Z",
                icon = "comment",
                color = "secondary"
            ),
            ActivityDto(
                id = "3",
                type = "like",
                title = "点赞了文章",
                description = "《前端开发最佳实践》",
                timestamp = "2024-01-13T09:15:00Z",
                icon = "favorite",
                color = "accent"
            )
        )

        return ProfileResponse(
            profile = profileDto,
            activities = activities
        )
    }

    /**
     * 更新用户资料
     */
    fun updateProfile(userId: String, request: UpdateProfileRequest): ProfileDto {
        val profile = userProfileRepository.findByUserId(userId)
            .orElseThrow { UserProfileNotFoundException(userId) }

        val updatedProfileEntity = profile.copy(
            name = request.name ?: profile.name,
            avatar = request.avatar ?: profile.avatar,
            email = request.email ?: profile.email,
            bio = request.bio ?: profile.bio
        )

        val savedProfile = userProfileRepository.save(updatedProfileEntity)

        return savedProfile.toDtoInternal()
    }

    private fun UserProfile.toDtoInternal(): ProfileDto {
        return ProfileDto(
            id = this.id,
            userId = this.userId,
            name = this.name,
            avatar = this.avatar,
            email = this.email,
            joinDate = this.joinDate,
            bio = this.bio,
            stats = ProfileStatsDto(
                posts = this.postsCount,
                followers = this.followersCount,
                following = this.followingCount
            )
        )
    }
}
