:host {
  display: block;
  --mat-list-active-indicator-shape: 0;
}

[mat-list-item] {
  --mat-list-list-item-leading-icon-start-space: var(--md-sys-spacing-1);
  --mat-list-list-item-leading-icon-end-space: var(--md-sys-spacing-1);
  border-left: 3px solid transparent;
  position: relative;

  &.active {
    background-color: var(--md-sys-color-secondary-container);
    border-color: var(--md-sys-color-primary);
  }

  // 菜单按钮
  [mat-icon-button] {
    display: inline-flex;
    align-items: center;
    font-size: var(--mdc-list-list-item-leading-icon-size, 24px);
  }

  // 操作按钮区域
  .action-buttons {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-1);
    margin-left: 0;
    margin-right: var(--md-sys-spacing-1);

    mat-icon {
      font-size: 20px;
      height: 24px;
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.2s ease, color 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.pin-indicator {
        color: var(--md-sys-color-primary);
      }

      &.unpinned {
        color: var(--md-sys-color-on-surface-variant);
        opacity: 0.6;

        &:hover {
          color: var(--md-sys-color-primary);
          opacity: 1;
        }
      }
    }
  }
}

// 删除按钮的特殊样式
::ng-deep {
  .delete-action {
    color: var(--md-sys-color-error);
  }

  // 工具提示样式
  .mat-tooltip {
    font-size: var(--md-sys-typescale-body-small-size);
    margin-top: 4px !important;
  }
}
