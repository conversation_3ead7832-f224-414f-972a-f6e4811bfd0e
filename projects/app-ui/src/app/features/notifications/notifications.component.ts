import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatTabsModule} from '@angular/material/tabs';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatBadgeModule} from '@angular/material/badge';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {NotificationService} from '../../api/notification.service';
import {Notification, NotificationResponse} from '../../core/models/notification.model';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './notifications.component.html',
  styleUrl: './notifications.component.scss',
})
export class NotificationsComponent implements OnInit {
  // 消息数据
  notifications: Notification[] = [];

  // 私信数据
  privateMessages: Notification[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  constructor(private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.loadMessagesData();
  }

  /**
   * 加载消息数据
   */
  loadMessagesData(): void {
    this.loading = true;
    this.error = null;

    this.notificationService.getNotifications().subscribe({
      next: (data: NotificationResponse) => {
        this.notifications = data.notifications || [];
        this.privateMessages = data.privateMessages || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取消息数据失败:', err);
        this.error = '获取消息数据失败，请稍后再试';
        this.loading = false;
      },
    });
  }

  // 获取未读消息数量
  getUnreadCount(messages: Notification[]): number {
    return messages.filter(msg => !msg.read).length;
  }
}
