package biz.zhizuo.ai.assistant.dto

import java.time.LocalDateTime

/**
 * 会话 DTO
 */
data class ChatSessionDto(
    val id: String?,
    val name: String,
    val userId: String,
    val pinned: Boolean = false,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val lastMessageAt: LocalDateTime?
)

/**
 * 创建会话请求 DTO
 */
data class CreateSessionRequest(
    val name: String
)

/**
 * 更新会话请求 DTO
 */
data class UpdateSessionRequest(
    val name: String? = null,
    val pinned: Boolean? = null
)
