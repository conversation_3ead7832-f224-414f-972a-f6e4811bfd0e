package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天消息实体
 */
@Entity
@Table(name = "chat_messages")
@EntityListeners(AuditingEntityListener::class)
data class ChatMessage(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    val session: ChatSession,

    @ManyToOne
    val user: User,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val role: MessageRole,

    @Column(columnDefinition = "TEXT", nullable = false)
    val content: String,

    @Column(name = "is_generating")
    val isGenerating: Boolean = false,

    @ManyToOne
    val replyTo: ChatMessage? = null,

    @OneToMany(mappedBy = "replyTo")
    val replies: List<ChatMessage> = emptyList(),

    @OneToOne
    var activeReply: ChatMessage? = null,

    val isArchived: Boolean = false,

    @Column(name = "assistant_name")
    val assistantName: String? = null,

    @Column(name = "liked")
    val liked: Boolean? = null,

    @Column(name = "disliked")
    val disliked: Boolean? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val steps: List<ChatMessageGenerationStep> = emptyList(),

    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val attachments: List<Attachment> = emptyList(),

    @OneToOne(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val feedback: ChatMessageFeedback? = null,
)

/**
 * 消息角色枚举
 */
enum class MessageRole {
    USER,
    ASSISTANT,
    SYSTEM,
}
