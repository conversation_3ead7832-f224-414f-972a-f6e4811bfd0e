import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {NotificationResponse} from './../core/models/notification.model';

/**
 * 通知服务
 * 提供与通知相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取通知数据
   * @returns 通知数据
   */
  getNotifications(): Observable<NotificationResponse> {
    return this.http.get<NotificationResponse>('/api/notifications');
  }
}
