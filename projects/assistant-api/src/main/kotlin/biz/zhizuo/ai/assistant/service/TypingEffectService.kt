package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.TypingMessageChunkDto
import biz.zhizuo.ai.assistant.repository.ChatMessageRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap

/**
 * 打字效果服务
 * 负责管理消息的打字效果显示，缓存服务端消息并控制前端显示速度
 */
@Service
class TypingEffectService(
    private val sseService: SseService,
    private val chatMessageRepository: ChatMessageRepository
) {

    private val logger = LoggerFactory.getLogger(TypingEffectService::class.java)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // 消息缓存：messageId -> 消息块列表
    private val messageCache = ConcurrentHashMap<String, MutableList<TypingMessageChunkDto>>()

    // 正在显示的消息：messageId -> 当前显示的块索引
    private val displayingMessages = ConcurrentHashMap<String, Int>()

    // 打字速度配置（每个字符的显示间隔，毫秒）
    private val typingSpeedMs = 50L

    // 块之间的延迟（毫秒）
    private val chunkDelayMs = 100L

    /**
     * 缓存消息块
     */
    fun cacheMessageChunk(messageId: String, text: String, isComplete: Boolean = false) {
        val chunks = messageCache.computeIfAbsent(messageId) { mutableListOf() }

        val chunkIndex = chunks.size
        val chunk = TypingMessageChunkDto(
            messageId = messageId,
            chunkIndex = chunkIndex,
            text = text,
            isComplete = isComplete,
            timestamp = LocalDateTime.now()
        )

        chunks.add(chunk)

        logger.debug("缓存消息块: messageId=$messageId, chunkIndex=$chunkIndex, textLength=${text.length}")

        // 如果这是第一个块，开始显示
        if (chunkIndex == 0) {
            startTypingEffect(messageId)
        }
    }

    /**
     * 开始打字效果
     */
    private fun startTypingEffect(messageId: String) {
        if (displayingMessages.containsKey(messageId)) {
            logger.debug("消息 $messageId 已在显示中，跳过")
            return
        }

        displayingMessages[messageId] = 0

        coroutineScope.launch {
            displayTypingEffect(messageId)
        }
    }

    /**
     * 显示打字效果
     */
    private suspend fun displayTypingEffect(messageId: String) {
        try {
            val userId = extractUserIdFromMessageId(messageId)
            val sseKey = "$userId:$messageId"

            var currentChunkIndex = 0
            var currentCharIndex = 0

            while (true) {
                val chunks = messageCache[messageId] ?: break

                // 检查是否有新的块可以显示
                if (currentChunkIndex >= chunks.size) {
                    // 检查是否已完成
                    val lastChunk = chunks.lastOrNull()
                    if (lastChunk?.isComplete == true) {
                        break
                    }

                    // 等待新的块
                    delay(chunkDelayMs)
                    continue
                }

                val currentChunk = chunks[currentChunkIndex]
                val text = currentChunk.text

                // 逐字符显示当前块
                while (currentCharIndex < text.length) {
                    val displayText = text.substring(0, currentCharIndex + 1)

                    // 发送打字效果事件
                    sendTypingEvent(sseKey, messageId, displayText, currentChunkIndex, false)

                    currentCharIndex++
                    delay(typingSpeedMs)
                }

                // 当前块显示完成，移动到下一块
                currentChunkIndex++
                currentCharIndex = 0

                // 块之间的延迟
                if (currentChunkIndex < chunks.size) {
                    delay(chunkDelayMs)
                }
            }

            // 发送完成事件
            val allText = messageCache[messageId]?.joinToString("") { it.text } ?: ""
            sendTypingEvent(sseKey, messageId, allText, currentChunkIndex - 1, true)

            logger.info("消息 $messageId 打字效果显示完成")

        } catch (e: Exception) {
            logger.error("显示打字效果时出错: messageId=$messageId", e)
        } finally {
            // 清理
            displayingMessages.remove(messageId)
            messageCache.remove(messageId)
        }
    }

    /**
     * 发送打字效果事件
     */
    private fun sendTypingEvent(
        sseKey: String,
        messageId: String,
        text: String,
        chunkIndex: Int,
        isComplete: Boolean
    ) {
        val event = mapOf(
            "messageId" to messageId,
            "text" to text,
            "chunkIndex" to chunkIndex,
            "isComplete" to isComplete,
            "timestamp" to LocalDateTime.now().toString()
        )

        sseService.sendMessageEvent(sseKey, "typing-effect", event)
    }

    /**
     * 立即完成打字效果（用户可能想要跳过动画）
     */
    fun completeTypingImmediately(messageId: String) {
        val chunks = messageCache[messageId] ?: return
        val allText = chunks.joinToString("") { it.text }

        val userId = extractUserIdFromMessageId(messageId)
        val sseKey = "$userId:$messageId"

        sendTypingEvent(sseKey, messageId, allText, chunks.size - 1, true)

        // 清理
        displayingMessages.remove(messageId)
        messageCache.remove(messageId)

        logger.info("消息 $messageId 打字效果已立即完成")
    }

    /**
     * 设置消息完成状态
     */
    fun markMessageComplete(messageId: String) {
        val chunks = messageCache[messageId]
        if (chunks != null && chunks.isNotEmpty()) {
            val lastChunk = chunks.last()
            if (!lastChunk.isComplete) {
                chunks[chunks.size - 1] = lastChunk.copy(isComplete = true)
                logger.debug("标记消息 $messageId 为完成状态")
            }
        }
    }

    /**
     * 获取消息的当前显示状态
     */
    fun getMessageDisplayStatus(messageId: String): Map<String, Any> {
        val chunks = messageCache[messageId]
        val currentChunkIndex = displayingMessages[messageId]

        return mapOf(
            "messageId" to messageId,
            "totalChunks" to (chunks?.size ?: 0),
            "currentChunkIndex" to (currentChunkIndex ?: -1),
            "isDisplaying" to displayingMessages.containsKey(messageId),
            "isComplete" to (chunks?.lastOrNull()?.isComplete ?: false)
        )
    }

    /**
     * 清理指定消息的缓存
     */
    fun clearMessageCache(messageId: String) {
        messageCache.remove(messageId)
        displayingMessages.remove(messageId)
        logger.debug("清理消息缓存: $messageId")
    }

    /**
     * 清理所有缓存
     */
    fun clearAllCache() {
        messageCache.clear()
        displayingMessages.clear()
        logger.info("清理所有消息缓存")
    }

    /**
     * 从消息ID中提取用户ID
     */
    private fun extractUserIdFromMessageId(messageId: String): String {
        return try {
            val message = chatMessageRepository.findById(messageId).orElse(null)
            message?.user?.id ?: "default-user"
        } catch (e: Exception) {
            logger.warn("无法获取消息 $messageId 的用户ID: ${e.message}")
            "default-user"
        }
    }

    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): Map<String, Any> {
        return mapOf(
            "cachedMessages" to messageCache.size,
            "displayingMessages" to displayingMessages.size,
            "totalCachedChunks" to messageCache.values.sumOf { it.size }
        )
    }
}
