package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.ChatSessionDto
import biz.zhizuo.ai.assistant.dto.CreateSessionRequest
import biz.zhizuo.ai.assistant.dto.UpdateSessionRequest
import biz.zhizuo.ai.assistant.security.UserPrincipal
import biz.zhizuo.ai.assistant.service.ChatSessionService
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.util.concurrent.ConcurrentHashMap

/**
 * 聊天会话控制器
 */
@RestController
@RequestMapping("/api/sessions")
class ChatSessionController(
    private val chatSessionService: ChatSessionService
) {

    // 存储SSE连接
    private val sseEmitters = ConcurrentHashMap<String, MutableList<SseEmitter>>()

    /**
     * 获取所有会话
     */
    @GetMapping
    fun getSessions(@AuthenticationPrincipal userPrincipal: UserPrincipal): ResponseEntity<List<ChatSessionDto>> {
        val sessions = chatSessionService.getUserSessions(userPrincipal.id)
        return ResponseEntity.ok(sessions)
    }

    /**
     * 获取单个会话
     */
    @GetMapping("/{id}")
    fun getSession(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<ChatSessionDto> {
        val session = chatSessionService.getSessionDtoById(id, userPrincipal.id)
        return ResponseEntity.ok(session)
    }

    /**
     * 创建新会话
     */
    @PostMapping
    fun createSession(
        @RequestBody request: CreateSessionRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<ChatSessionDto> {
        val session = chatSessionService.createSession(request, userPrincipal.id)
        
        // 发送会话创建事件
        sendSessionEvent(userPrincipal.id, "created", session)
        
        return ResponseEntity.status(HttpStatus.CREATED).body(session)
    }

    /**
     * 更新会话
     */
    @PatchMapping("/{id}")
    fun updateSession(
        @PathVariable id: String,
        @RequestBody request: UpdateSessionRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<ChatSessionDto> {
        val session = chatSessionService.updateSession(id, request, userPrincipal.id)
            ?: return ResponseEntity.notFound().build()
        
        // 发送会话更新事件
        sendSessionEvent(userPrincipal.id, "updated", session)
        
        return ResponseEntity.ok(session)
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/{id}")
    fun deleteSession(
        @PathVariable id: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<Void> {
        val deleted = chatSessionService.deleteSession(id, userPrincipal.id)
        if (!deleted) {
            return ResponseEntity.notFound().build()
        }
        
        // 发送会话删除事件
        sendSessionDeleteEvent(userPrincipal.id, id)
        
        return ResponseEntity.noContent().build()
    }

    /**
     * 获取会话事件流 (SSE)
     */
    @GetMapping("/events", produces = [MediaType.TEXT_EVENT_STREAM_VALUE])
    fun getEventStream(
        @RequestParam token: String,
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): SseEmitter {
        val emitter = SseEmitter(Long.MAX_VALUE)
        val userId = userPrincipal.id

        // 添加到连接列表
        sseEmitters.computeIfAbsent(userId) { mutableListOf() }.add(emitter)

        // 设置完成和超时回调
        emitter.onCompletion {
            removeEmitter(userId, emitter)
        }
        emitter.onTimeout {
            removeEmitter(userId, emitter)
        }
        emitter.onError {
            removeEmitter(userId, emitter)
        }

        return emitter
    }

    /**
     * 发送会话事件
     */
    private fun sendSessionEvent(userId: String, eventType: String, session: ChatSessionDto) {
        val emitters = sseEmitters[userId] ?: return
        val deadEmitters = mutableListOf<SseEmitter>()

        emitters.forEach { emitter ->
            try {
                emitter.send(
                    SseEmitter.event()
                        .name(eventType)
                        .data(session)
                )
            } catch (e: Exception) {
                deadEmitters.add(emitter)
            }
        }

        // 移除失效的连接
        deadEmitters.forEach { removeEmitter(userId, it) }
    }

    /**
     * 发送会话删除事件
     */
    private fun sendSessionDeleteEvent(userId: String, sessionId: String) {
        val emitters = sseEmitters[userId] ?: return
        val deadEmitters = mutableListOf<SseEmitter>()

        emitters.forEach { emitter ->
            try {
                emitter.send(
                    SseEmitter.event()
                        .name("deleted")
                        .data(sessionId)
                )
            } catch (e: Exception) {
                deadEmitters.add(emitter)
            }
        }

        // 移除失效的连接
        deadEmitters.forEach { removeEmitter(userId, it) }
    }

    /**
     * 移除SSE连接
     */
    private fun removeEmitter(userId: String, emitter: SseEmitter) {
        sseEmitters[userId]?.remove(emitter)
        if (sseEmitters[userId]?.isEmpty() == true) {
            sseEmitters.remove(userId)
        }
    }
}
