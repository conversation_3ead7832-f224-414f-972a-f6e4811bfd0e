package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天会话实体
 */
@Entity
@Table(name = "chat_sessions")
@EntityListeners(AuditingEntityListener::class)
data class ChatSession(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    val user: User,

    @Column(nullable = false, length = 200)
    val name: String,

    @Column(name = "pinned")
    val pinned: Boolean = false,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @Column(name = "last_message_at")
    val lastMessageAt: LocalDateTime? = null,
) {
    @OneToMany(mappedBy = "session")
    val messages: List<ChatMessage> = emptyList()
}
