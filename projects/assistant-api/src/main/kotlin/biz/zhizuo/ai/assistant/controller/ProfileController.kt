package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.ProfileDto
import biz.zhizuo.ai.assistant.dto.ProfileResponse
import biz.zhizuo.ai.assistant.dto.UpdateProfileRequest
import biz.zhizuo.ai.assistant.security.UserPrincipal
import biz.zhizuo.ai.assistant.service.ProfileService
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

/**
 * 用户资料控制器
 */
@RestController
@RequestMapping("/api/profile")
@CrossOrigin(origins = ["*"])
class ProfileController(
    private val profileService: ProfileService
) {

    /**
     * 获取用户资料
     */
    @GetMapping
    fun getProfile(
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<ProfileResponse> {
        val response = profileService.getProfile(userPrincipal.id)
        return ResponseEntity.ok(response)
    }

    /**
     * 更新用户资料
     */
    @PatchMapping
    fun updateProfile(
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
        @Valid @RequestBody request: UpdateProfileRequest
    ): ResponseEntity<ProfileDto> {
        val updatedProfile = profileService.updateProfile(userPrincipal.id, request)
        return ResponseEntity.ok(updatedProfile)
    }
}
