import {Component, inject, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';
import {MatSidenav, MatSidenavModule} from '@angular/material/sidenav';
import {RouterOutlet} from '@angular/router';
import {map, shareReplay} from 'rxjs/operators';
import {CommonModule} from '@angular/common';
import {Subscription} from 'rxjs';

// 导入子组件
import {HeaderComponent} from './components/header/header.component';
import {SidenavComponent} from './components/sidenav/sidenav.component';
import {UserMenuComponent} from './components/user-menu/user-menu.component';
import {DirectAccessComponent} from './components/direct-access/direct-access.component';
import {NotificationService} from '../api/notification.service';
import {AuthService} from '../core/services/auth.service';
import {Notification} from '../core/models/notification.model';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
  imports: [
    CommonModule,
    MatSidenavModule,
    RouterOutlet,
    HeaderComponent,
    SidenavComponent,
    UserMenuComponent,
    DirectAccessComponent,
  ],
  standalone: true,
})
export class LayoutComponent implements OnInit, OnDestroy {
  @ViewChild('drawer') drawer!: MatSidenav;

  private breakpointObserver = inject(BreakpointObserver);
  private notificationService = inject(NotificationService);
  private authService = inject(AuthService);

  protected isHandset = false;
  protected showMobileSearch = false;
  protected notificationCount = 0;

  // 订阅
  private breakpointSubscription: Subscription | null = null;
  private authSubscription: Subscription | null = null;

  constructor() {
    // 初始化时检查一次
    this.isHandset = this.breakpointObserver.isMatched(Breakpoints.Handset);

    // 监听屏幕尺寸变化
    this.breakpointSubscription = this.breakpointObserver.observe([
      Breakpoints.Handset,
      Breakpoints.TabletPortrait,
    ]).pipe(
        map(result => result.matches),
        shareReplay(),
    ).subscribe(matches => {
      this.isHandset = matches;
      console.log('Is handset:', this.isHandset);

      // 当切换到非移动设备时，隐藏移动搜索框
      if (!matches) {
        this.showMobileSearch = false;
      }
    });
  }

  ngOnInit(): void {
    // 订阅用户登录状态变化
    this.authSubscription = this.authService.isLoggedIn$.subscribe(isLoggedIn => {
      if (isLoggedIn) {
        // 用户已登录，获取未读消息数量
        this.loadNotificationCount();
      } else {
        // 用户未登录，重置消息数量为0
        this.notificationCount = 0;
      }
    });
  }

  ngOnDestroy(): void {
    // 取消订阅
    if (this.breakpointSubscription) {
      this.breakpointSubscription.unsubscribe();
      this.breakpointSubscription = null;
    }

    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
      this.authSubscription = null;
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  protected toggleSidenav(): void {
    this.drawer.toggle();
  }

  /**
   * 切换移动设备上的搜索框显示状态
   */
  protected toggleMobileSearch(): void {
    this.showMobileSearch = !this.showMobileSearch;
  }

  /**
   * 加载未读消息数量
   */
  private loadNotificationCount(): void {
    this.notificationService.getNotifications().subscribe({
      next: (data) => {
        // 计算未读通知数量
        const unreadNotifications = data.notifications?.filter((msg: Notification) => !msg.read) || [];
        const unreadPrivateMessages = data.privateMessages?.filter((msg: Notification) => !msg.read) || [];

        // 设置总的未读消息数量
        this.notificationCount = unreadNotifications.length + unreadPrivateMessages.length;
      },
      error: (err) => {
        console.error('获取消息数据失败:', err);
        // 出错时设置为0
        this.notificationCount = 0;
      },
    });
  }
}
