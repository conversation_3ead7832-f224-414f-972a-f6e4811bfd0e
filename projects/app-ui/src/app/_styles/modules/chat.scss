// 聊天模块样式
// 定义聊天相关的通用样式

// ===== 聊天容器 =====
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--md-sys-color-background);
}

// ===== 消息列表 =====
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--md-sys-spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

// ===== 消息气泡基础样式 =====
.message-bubble {
  max-width: 70%;
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  position: relative;
  word-wrap: break-word;
  line-height: var(--md-sys-typescale-line-height-normal);

  // 用户消息气泡
  &.user-message {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    align-self: flex-end;
    border-bottom-right-radius: var(--md-sys-shape-corner-extra-small);

    // 气泡尾巴
    &::after {
      content: '';
      position: absolute;
      right: -6px;
      bottom: 8px;
      width: 12px;
      height: 12px;
      background-color: var(--md-sys-color-primary);
      transform: rotate(45deg);
      border-radius: var(--md-sys-shape-corner-extra-small);
    }
  }

  // 助手消息气泡
  &.assistant-message {
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
    align-self: flex-start;
    border: 1px solid var(--md-sys-color-outline-variant);
    border-bottom-left-radius: var(--md-sys-shape-corner-extra-small);

    // 气泡尾巴
    &::before {
      content: '';
      position: absolute;
      left: -7px;
      bottom: 8px;
      width: 12px;
      height: 12px;
      background-color: var(--md-sys-color-surface);
      border: 1px solid var(--md-sys-color-outline-variant);
      border-top: none;
      border-right: none;
      transform: rotate(45deg);
      border-radius: var(--md-sys-shape-corner-extra-small);
    }
  }
}

// ===== 头像样式 =====
.message-avatar {
  width: var(--md-sys-layout-avatar-size);
  height: var(--md-sys-layout-avatar-size);
  border-radius: var(--md-sys-shape-corner-small);
  overflow: hidden;
  flex-shrink: 0;

  img {

    height: 100%;
    object-fit: cover;
  }

  &.user-avatar {
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
  }

  &.assistant-avatar {
    background-color: var(--md-sys-color-secondary-container);
    color: var(--md-sys-color-on-secondary-container);
  }
}

// ===== 消息操作按钮 =====
.message-actions {
  display: flex;
  gap: var(--md-sys-spacing-1);
  margin-top: var(--md-sys-spacing-2);
  transition: opacity var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  .action-btn {
    width: 28px;
    height: 28px;

    border: none;
    background: none;
    border-radius: var(--md-sys-shape-corner-small);
    color: var(--md-sys-color-on-surface-variant);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      background-color: var(--md-sys-color-surface-variant);
      color: var(--md-sys-color-on-surface);
    }

    &.active {
      color: var(--md-sys-color-primary);
      background-color: var(--md-sys-color-primary-container);
    }

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }
}

// 消息悬停时显示操作按钮
.message-container:hover .message-actions {
  opacity: 1;
}

// ===== 聊天输入区域 =====
.chat-input-area {
  padding: var(--md-sys-spacing-4);
  background-color: var(--md-sys-color-surface);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.chat-input-container {
  display: flex;
  align-items: flex-end;
  gap: var(--md-sys-spacing-3);
  max-width: var(--md-sys-layout-content-max-width);
  margin: 0 auto;
}

.chat-input-field {
  flex: 1;

  .chat-textarea {
    min-height: 44px;
    max-height: 120px;
    resize: none;
    border-radius: var(--md-sys-shape-corner-medium);
    border: 1px solid var(--md-sys-color-outline);
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
    font-family: var(--md-sys-typescale-font-family-base);
    font-size: var(--md-sys-typescale-body-medium-size);
    line-height: var(--md-sys-typescale-line-height-normal);

    &:focus {
      outline: none;
      border-color: var(--md-sys-color-primary);
      box-shadow: 0 0 0 1px var(--md-sys-color-primary);
    }
  }
}

.chat-send-btn {
  width: 44px;
  height: 44px;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover:not(:disabled) {
    background-color: var(--md-sys-color-on-primary-container);
    transform: scale(1.05);
  }

  &:disabled {
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface-variant);
    cursor: not-allowed;
    opacity: 0.6;
  }

  mat-icon {
    font-size: var(--md-sys-layout-icon-size-medium);
    width: var(--md-sys-layout-icon-size-medium);
    height: var(--md-sys-layout-icon-size-medium);
  }
}

// ===== 附件预览 =====
.attachment-preview {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-2);
  margin-bottom: var(--md-sys-spacing-3);

  .attachment-item {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-2);
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
    border-radius: var(--md-sys-shape-corner-small);
    font-size: var(--md-sys-typescale-body-small-size);

    .attachment-name {
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .remove-attachment {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;

      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: var(--md-sys-layout-icon-size-small);
        width: var(--md-sys-layout-icon-size-small);
        height: var(--md-sys-layout-icon-size-small);
      }
    }
  }
}

// ===== 建议模板 =====
.suggestion-templates {
  padding: var(--md-sys-spacing-4);
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.04);
  border-radius: var(--md-sys-shape-corner-medium);
  margin: var(--md-sys-spacing-4);

  .templates-title {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-on-surface-variant);
    margin-bottom: var(--md-sys-spacing-3);
    font-weight: var(--md-sys-typescale-font-weight-medium);
  }

  .templates-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--md-sys-spacing-2);
  }

  .template-btn {
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-corner-large);
    font-size: var(--md-sys-typescale-body-small-size);
    cursor: pointer;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      background-color: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
      border-color: var(--md-sys-color-primary);
    }
  }
}

// ===== 生成状态指示器 =====
.generation-indicator {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  margin: var(--md-sys-spacing-2) 0;

  .indicator-text {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .loading-dots {
    display: flex;
    gap: var(--md-sys-spacing-1);

    .dot {
      width: 6px;
      height: 6px;
      border-radius: var(--md-sys-shape-corner-full);
      background-color: var(--md-sys-color-primary);
      animation: loading-pulse 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

@keyframes loading-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// ===== 响应式调整 =====
@media (max-width: 768px) {
  .chat-messages {
    padding: var(--md-sys-spacing-3);
    gap: var(--md-sys-spacing-3);
  }

  .chat-input-area {
    padding: var(--md-sys-spacing-3);
  }

  .message-bubble {
    max-width: 85%;
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
  }

  .suggestion-templates {
    margin: var(--md-sys-spacing-3);
    padding: var(--md-sys-spacing-3);
  }
}

@media (max-width: 599px) {
  .chat-input-container {
    gap: var(--md-sys-spacing-2);
  }

  .chat-send-btn {
    width: 40px;
    height: 40px;
  }

  .message-bubble {
    max-width: 90%;
  }
}
