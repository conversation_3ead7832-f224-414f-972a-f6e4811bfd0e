<div class="messages-container">
  <h1>消息中心</h1>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadMessagesData()">重试</button>
    </div>
  }

  <!-- 消息内容 -->
  @if (!loading && !error) {
    <mat-tab-group animationDuration="0ms">
      <mat-tab>
        <ng-template mat-tab-label>
          <span>通知</span>
          @if (getUnreadCount(notifications) > 0) {
            <span class="tab-badge">{{ getUnreadCount(notifications) }}</span>
          }
        </ng-template>

        <div class="tab-content">
          <div class="message-actions">
            <button mat-flat-button color="primary">
              <mat-icon matButtonIcon svgIcon="done-all"></mat-icon>
              全部标为已读
            </button>
            <button mat-flat-button color="warn">
              <mat-icon matButtonIcon svgIcon="delete"></mat-icon>
              清空通知
            </button>
          </div>

          @if (notifications.length === 0) {
            <p class="empty-state">暂无通知</p>
          } @else {
            <div class="message-list">
              @for (notification of notifications; track notification.id) {
                <mat-card appearance="outlined" [ngClass]="{'unread': !notification.read}">
                  <mat-card-content>
                    <div class="message-item">
                      <div class="icon-thumb message-icon" [ngClass]="notification.type">
                        <mat-icon [svgIcon]="notification.type??'notification'" matButtonIcon></mat-icon>
                      </div>
                      <div class="message-content">
                        <div class="message-header">
                          <h3>{{ notification.title }}</h3>
                          <span class="message-time">{{ notification.time }}</span>
                        </div>
                        <p>{{ notification.content }}</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              }
            </div>
          }
        </div>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>
          <span>私信</span>
          @if (getUnreadCount(privateMessages) > 0) {
            <span class="tab-badge">{{ getUnreadCount(privateMessages) }}</span>
          }
        </ng-template>

        <div class="tab-content">
          @if (privateMessages.length === 0) {
            <p class="empty-state">暂无私信</p>
          } @else {
            <div class="message-list">
              @for (message of privateMessages; track message.id) {
                <mat-card appearance="outlined" [ngClass]="{'unread': !message.read}">
                  <mat-card-content>
                    <div class="message-item">
                      <div class="message-avatar">
                        <img [src]="message.avatar ?? '/assets/default-avatar.svg'" alt="头像">
                      </div>
                      <div class="message-content">
                        <div class="message-header">
                          <h3>{{ message.sender }}</h3>
                          <span class="message-time">{{ message.time }}</span>
                        </div>
                        <p>{{ message.content }}</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              }
            </div>
          }
        </div>
      </mat-tab>
    </mat-tab-group>
  }
</div>
