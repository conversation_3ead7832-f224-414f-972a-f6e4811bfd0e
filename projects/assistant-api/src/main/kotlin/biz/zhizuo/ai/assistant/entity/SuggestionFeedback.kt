package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UuidGenerator
import java.time.OffsetDateTime

/**
 * Entity representing a record of a user selecting a suggestion.
 * This data is used to analyze suggestion effectiveness and improve future recommendations.
 */
@Entity
@Table(name = "suggestion_feedbacks")
data class SuggestionFeedback(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    var id: String? = null,

    @Column(name = "suggestion_text", nullable = false, columnDefinition = "TEXT")
    var suggestionText: String,

    @Column(name = "is_report_template", nullable = false)
    var isReportTemplate: Boolean = false,

    @Column(name = "message_id", nullable = false)
    var messageId: String,

    @Column(name = "session_id", nullable = false)
    var sessionId: String,

    @Column(name = "suggestion_id", nullable = false)
    var suggestionId: String,

    @Column(name = "user_id") // 关联的用户ID，可以为空（如果建议是在非登录状态下提供和点击的）
    var userId: String? = null,

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: OffsetDateTime? = null,
)
