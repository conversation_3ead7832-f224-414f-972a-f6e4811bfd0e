package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.SettingsResponse
import biz.zhizuo.ai.assistant.dto.UpdateSettingsRequest
import biz.zhizuo.ai.assistant.dto.UserSettingsDto
import biz.zhizuo.ai.assistant.entity.UserSettings
import biz.zhizuo.ai.assistant.repository.UserSettingsRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 设置服务
 */
@Service
@Transactional
class SettingsService(
    private val userSettingsRepository: UserSettingsRepository
) {

    /**
     * 获取用户设置
     */
    @Transactional(readOnly = true)
    fun getSettings(userId: String): SettingsResponse {
        val settings = userSettingsRepository.findByUserId(userId)
            .orElseGet {
                // 如果没有设置记录，创建默认设置
                val defaultSettings = UserSettings(userId = userId)
                userSettingsRepository.save(defaultSettings)
            }
        return SettingsResponse(settings = settings.toDto())
    }

    /**
     * 更新用户设置
     */
    fun updateSettings(userId: String, request: UpdateSettingsRequest): SettingsResponse {
        val settings = userSettingsRepository.findByUserId(userId)
            .orElseGet {
                UserSettings(userId = userId)
            }

        val updatedSettings = settings.copy(
            themeMode = request.themeMode ?: settings.themeMode,
            language = request.language ?: settings.language,
            notificationsEnabled = request.notificationsEnabled ?: settings.notificationsEnabled,
            emailNotifications = request.emailNotifications ?: settings.emailNotifications,
            pushNotifications = request.pushNotifications ?: settings.pushNotifications,
            privacyProfilePublic = request.privacyProfilePublic ?: settings.privacyProfilePublic,
            privacyShowEmail = request.privacyShowEmail ?: settings.privacyShowEmail,
            privacyShowActivity = request.privacyShowActivity ?: settings.privacyShowActivity
        )

        val savedSettings = userSettingsRepository.save(updatedSettings)
        return SettingsResponse(settings = savedSettings.toDto())
    }

    private fun UserSettings.toDto(): UserSettingsDto {
        return UserSettingsDto(
            id = this.id,
            userId = this.userId,
            themeMode = this.themeMode,
            language = this.language,
            notificationsEnabled = this.notificationsEnabled,
            emailNotifications = this.emailNotifications,
            pushNotifications = this.pushNotifications,
            privacyProfilePublic = this.privacyProfilePublic,
            privacyShowEmail = this.privacyShowEmail,
            privacyShowActivity = this.privacyShowActivity
        )
    }
}
