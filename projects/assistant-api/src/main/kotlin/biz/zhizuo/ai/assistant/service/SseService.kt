package biz.zhizuo.ai.assistant.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

@Service
class SseService {

    private val logger = LoggerFactory.getLogger(SseService::class.java)
    private val sseEmitters = ConcurrentHashMap<String, CopyOnWriteArrayList<SseEmitter>>()

    /**
     * 注册 SSE Emitter
     * @param key 通常是 userId:entityId 这样的组合，用于唯一标识一个事件流订阅
     * @param emitter SseEmitter 实例
     */
    fun registerSseEmitter(key: String, emitter: SseEmitter) {
        val list = sseEmitters.computeIfAbsent(key) { CopyOnWriteArrayList<SseEmitter>() }
        list.add(emitter)
        logger.info("SSE Emitter registered for key: {}", key)

        emitter.onCompletion {
            logger.info("SSE Emitter completed for key: {}", key)
            removeSseEmitter(key, emitter)
        }
        emitter.onTimeout {
            logger.info("SSE Emitter timed out for key: {}", key)
            removeSseEmitter(key, emitter)
        }
        emitter.onError { throwable ->
            logger.error("SSE Emitter error for key: {}: {}", key, throwable.message)
            removeSseEmitter(key, emitter)
        }

        // 尝试发送一个初始确认事件
        try {
            emitter.send(
                SseEmitter.event()
                    .name("sse-connection-established")
                    .data(mapOf("key" to key, "status" to "SSE connection established and initial ack sent."))
            )
            logger.info("Initial acknowledgment sent to SSE Emitter for key: {}", key)
        } catch (e: Exception) {
            logger.warn("Failed to send initial ack to SSE Emitter for key: {}. Removing emitter. Error: {}", key, e.message)
            removeSseEmitter(key, emitter) // 如果初始发送失败，也移除
        }
    }

    /**
     * 注销 SSE Emitter
     */
    private fun removeSseEmitter(key: String, emitter: SseEmitter) {
        val list = sseEmitters[key]
        if (list != null) {
            val removed = list.remove(emitter)
            if (removed) {
                logger.info("SSE Emitter removed for key: {}. Remaining emitters for this key: {}", key, list.size)
                if (list.isEmpty()) {
                    sseEmitters.computeIfPresent(key) { _, currentListInMap ->
                        if (currentListInMap.isEmpty()) {
                            logger.info("No more emitters for key: {}, removing key from map.", key)
                            null // Remove mapping if empty
                        } else {
                            currentListInMap // Keep it if not empty
                        }
                    }
                }
            } else {
                logger.warn("Attempted to remove a non-existent or already removed SSE Emitter for key: {}", key)
            }
        } else {
            logger.warn("Attempted to remove SSE Emitter for a non-existent key: {}", key)
        }
    }

    /**
     * 发送消息事件到指定key的所有订阅者
     * @param key 目标订阅者key
     * @param eventType 事件类型 (event name)
     * @param data 事件数据
     */
    fun sendMessageEvent(key: String, eventType: String, data: Any) {
        val emittersForKey = sseEmitters[key]
        if (emittersForKey == null || emittersForKey.isEmpty()) {
            logger.debug("No active SSE Emitters for key: {} to send event: {}", key, eventType)
            return
        }

        logger.info("Sending SSE event '{}' to {} emitters for key: {}", eventType, emittersForKey.size, key)
        emittersForKey.forEach { emitter ->
            try {
                emitter.send(
                    SseEmitter.event()
                        .name(eventType)
                        .data(data)
                )
            } catch (e: Exception) {
                logger.warn("Failed to send SSE event to emitter for key: {}. Error: {}. Removing emitter.", key, e.message)
                // 异步移除，避免在迭代中修改集合导致问题，尽管CopyOnWriteArrayList的迭代器是快照式的
                // 但onError回调本身会处理移除，这里可以看作是额外的保障或记录
                // 实际上，emitter.onError应该已经被触发并处理了移除。
                 removeSseEmitter(key, emitter) // 确保有问题的 emitter 被移除
            }
        }
    }

    /**
     * 获取指定key的活跃Emitter数量 (主要用于测试或监控)
     */
    fun getActiveEmitterCount(key: String): Int {
        return sseEmitters[key]?.size ?: 0
    }

    /**
     * 获取所有活跃Emitter的总数 (主要用于测试或监控)
     */
    fun getTotalActiveEmitters(): Int {
        return sseEmitters.values.sumOf { it.size }
    }
}
