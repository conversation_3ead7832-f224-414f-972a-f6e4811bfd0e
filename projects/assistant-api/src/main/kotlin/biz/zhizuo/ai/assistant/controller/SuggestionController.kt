package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.SuggestionFeedbackRequest
import biz.zhizuo.ai.assistant.dto.SuggestionTemplateDto
import biz.zhizuo.ai.assistant.security.UserPrincipal
import biz.zhizuo.ai.assistant.service.SuggestionFeedbackService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/suggestions")
class SuggestionController(
    private val suggestionFeedbackService: SuggestionFeedbackService,
) {

    @GetMapping
    fun getSuggestions(
        @RequestParam sessionId: String,
        @RequestParam(required = false) messageId: String?,
    ): ResponseEntity<List<SuggestionTemplateDto>> {
        val suggestions = listOf(
            SuggestionTemplateDto(
                id = "explain_more",
                text = "请详细解释一下这个概念。",
                description = "请求更详细的解释"
            ),
            SuggestionTemplateDto(id = "give_example", text = "能给出一个具体的例子吗？", description = "请求示例"),
            SuggestionTemplateDto(id = "how_to_apply", text = "如何在实践中应用这个？", description = "询问实际应用")
        )
        return ResponseEntity.ok(suggestions)
    }

    @GetMapping("/reports")
    fun getReportTemplates(
        @RequestParam sessionId: String,
        @RequestParam(required = false) messageId: String?,
    ): ResponseEntity<List<SuggestionTemplateDto>> {
        val reportTemplates = listOf(
            SuggestionTemplateDto(
                id = "summary_report",
                text = "生成摘要报告",
                description = "生成对话的摘要报告",
                isReportTemplate = true,
                icon = "summarize"
            ),
            SuggestionTemplateDto(
                id = "action_items_report",
                text = "提取行动要点",
                description = "从对话中提取行动要点",
                isReportTemplate = true,
                icon = "checklist"
            )
        )
        return ResponseEntity.ok(reportTemplates)
    }

    @PostMapping("/feedback")
    fun submitFeedback(
        @RequestBody request: SuggestionFeedbackRequest,
        @AuthenticationPrincipal userPrincipal: UserPrincipal?, // 允许匿名反馈
    ): ResponseEntity<Map<String, String>> {
        suggestionFeedbackService.submitFeedback(request, userPrincipal)
        return ResponseEntity.status(HttpStatus.CREATED).body(mapOf("message" to "Feedback submitted successfully"))
    }
}
