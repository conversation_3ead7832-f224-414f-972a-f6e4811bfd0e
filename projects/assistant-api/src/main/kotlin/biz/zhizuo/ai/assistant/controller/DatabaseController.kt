package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.service.DatabaseService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 数据库管理控制器
 */
@RestController
@RequestMapping("/api/database")
class DatabaseController(
    private val databaseService: DatabaseService
) {

    /**
     * 重置数据库
     */
    @PostMapping("/reset")
    fun resetDatabase(): ResponseEntity<Map<String, String>> {
        return try {
            val message = databaseService.resetDatabase()
            ResponseEntity.ok(mapOf("message" to message))
        } catch (e: Exception) {
            ResponseEntity.internalServerError()
                .body(mapOf("error" to (e.message ?: "数据库重置失败")))
        }
    }
}
