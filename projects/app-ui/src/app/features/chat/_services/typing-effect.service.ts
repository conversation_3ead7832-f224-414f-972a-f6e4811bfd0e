import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * 打字效果消息块
 */
export interface TypingMessageChunk {
  messageId: string;
  chunkIndex: number;
  text: string;
  isComplete: boolean;
  timestamp: Date;
}

/**
 * 打字效果状态
 */
export interface TypingEffectState {
  messageId: string;
  displayedText: string;
  isTyping: boolean;
  isComplete: boolean;
  speed: number; // 每个字符的显示间隔（毫秒）
}

/**
 * 打字效果服务
 * 负责管理前端的打字效果显示
 */
@Injectable({
  providedIn: 'root'
})
export class TypingEffectService {
  
  // 当前正在显示打字效果的消息状态
  private typingStates = new Map<string, BehaviorSubject<TypingEffectState>>();
  
  // 消息缓存队列
  private messageQueues = new Map<string, TypingMessageChunk[]>();
  
  // 默认打字速度（毫秒）
  private readonly DEFAULT_TYPING_SPEED = 30;
  
  /**
   * 开始为指定消息显示打字效果
   */
  startTypingEffect(messageId: string, speed: number = this.DEFAULT_TYPING_SPEED): Observable<TypingEffectState> {
    if (!this.typingStates.has(messageId)) {
      const initialState: TypingEffectState = {
        messageId,
        displayedText: '',
        isTyping: false,
        isComplete: false,
        speed
      };
      this.typingStates.set(messageId, new BehaviorSubject(initialState));
      this.messageQueues.set(messageId, []);
    }
    
    return this.typingStates.get(messageId)!.asObservable();
  }
  
  /**
   * 添加消息块到队列
   */
  addMessageChunk(chunk: TypingMessageChunk): void {
    const queue = this.messageQueues.get(chunk.messageId);
    if (!queue) {
      console.warn(`消息 ${chunk.messageId} 的打字效果未初始化`);
      return;
    }
    
    queue.push(chunk);
    
    // 如果当前没有在显示，开始显示
    const state = this.typingStates.get(chunk.messageId);
    if (state && !state.value.isTyping && !state.value.isComplete) {
      this.processTypingQueue(chunk.messageId);
    }
  }
  
  /**
   * 处理打字队列
   */
  private async processTypingQueue(messageId: string): Promise<void> {
    const state = this.typingStates.get(messageId);
    const queue = this.messageQueues.get(messageId);
    
    if (!state || !queue) {
      return;
    }
    
    // 更新状态为正在打字
    state.next({
      ...state.value,
      isTyping: true
    });
    
    let displayedText = state.value.displayedText;
    
    while (queue.length > 0) {
      const chunk = queue.shift()!;
      
      // 逐字符显示当前块
      for (let i = 0; i < chunk.text.length; i++) {
        displayedText += chunk.text[i];
        
        // 更新显示的文本
        state.next({
          ...state.value,
          displayedText,
          isTyping: true
        });
        
        // 等待指定的时间间隔
        await this.delay(state.value.speed);
      }
      
      // 如果这是最后一个块且标记为完成
      if (chunk.isComplete && queue.length === 0) {
        state.next({
          ...state.value,
          displayedText,
          isTyping: false,
          isComplete: true
        });
        break;
      }
    }
    
    // 如果队列为空但还没完成，停止打字状态但保持等待
    if (queue.length === 0 && !state.value.isComplete) {
      state.next({
        ...state.value,
        isTyping: false
      });
    }
  }
  
  /**
   * 立即完成打字效果
   */
  completeTypingImmediately(messageId: string): void {
    const state = this.typingStates.get(messageId);
    const queue = this.messageQueues.get(messageId);
    
    if (!state || !queue) {
      return;
    }
    
    // 将所有剩余的块合并为最终文本
    let finalText = state.value.displayedText;
    while (queue.length > 0) {
      const chunk = queue.shift()!;
      finalText += chunk.text;
    }
    
    // 立即更新为完成状态
    state.next({
      ...state.value,
      displayedText: finalText,
      isTyping: false,
      isComplete: true
    });
  }
  
  /**
   * 获取消息的当前打字状态
   */
  getTypingState(messageId: string): TypingEffectState | null {
    const state = this.typingStates.get(messageId);
    return state ? state.value : null;
  }
  
  /**
   * 清理指定消息的打字效果
   */
  clearTypingEffect(messageId: string): void {
    const state = this.typingStates.get(messageId);
    if (state) {
      state.complete();
      this.typingStates.delete(messageId);
    }
    this.messageQueues.delete(messageId);
  }
  
  /**
   * 清理所有打字效果
   */
  clearAllTypingEffects(): void {
    this.typingStates.forEach(state => state.complete());
    this.typingStates.clear();
    this.messageQueues.clear();
  }
  
  /**
   * 设置打字速度
   */
  setTypingSpeed(messageId: string, speed: number): void {
    const state = this.typingStates.get(messageId);
    if (state) {
      state.next({
        ...state.value,
        speed
      });
    }
  }
  
  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 获取所有活跃的打字效果状态
   */
  getAllTypingStates(): Map<string, TypingEffectState> {
    const states = new Map<string, TypingEffectState>();
    this.typingStates.forEach((subject, messageId) => {
      states.set(messageId, subject.value);
    });
    return states;
  }
}
