package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.ChatSession
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * 聊天会话数据访问接口
 */
@Repository
interface ChatSessionRepository : JpaRepository<ChatSession, String> {

    /**
     * 根据用户ID查找所有会话，按更新时间倒序排列
     */
    fun findByUserIdOrderByUpdatedAtDesc(userId: String): List<ChatSession>

    /**
     * 根据用户ID和会话ID查找会话
     */
    fun findByIdAndUserId(id: String, userId: String): ChatSession?

    /**
     * 根据用户ID查找置顶的会话
     */
    fun findByUserIdAndPinnedTrueOrderByUpdatedAtDesc(userId: String): List<ChatSession>

    /**
     * 根据用户ID查找非置顶的会话
     */
    fun findByUserIdAndPinnedFalseOrderByUpdatedAtDesc(userId: String): List<ChatSession>

    /**
     * 统计用户的会话数量
     */
    fun countByUserId(userId: String): Long

    /**
     * 删除用户的所有会话
     */
    fun deleteByUserId(userId: String)
}
