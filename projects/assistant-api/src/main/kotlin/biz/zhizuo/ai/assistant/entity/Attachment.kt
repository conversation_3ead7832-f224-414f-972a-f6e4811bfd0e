package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 附件实体
 */
@Entity
@Table(name = "attachments")
@EntityListeners(AuditingEntityListener::class)
data class Attachment(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    val message: ChatMessage,

    @Column(nullable = false)
    val name: String,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val type: AttachmentType,

    @Column(name = "file_size")
    val fileSize: Long? = null,

    @Column(name = "mime_type")
    val mimeType: String? = null,

    @Column(name = "file_path")
    val filePath: String? = null,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 附件类型枚举
 */
enum class AttachmentType {
    IMAGE, DOCUMENT, TEXT, URL, OTHER
}
