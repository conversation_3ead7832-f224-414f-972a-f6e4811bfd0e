/**
 * 聊天搜索结果
 */
export interface ChatSearchResult {
  /**
   * 会话ID
   */
  sessionId: string;

  /**
   * 会话名称
   */
  sessionName: string;

  /**
   * 消息ID
   */
  messageId: string;

  /**
   * 消息内容片段，包含高亮的HTML
   */
  snippet: string;

  /**
   * 消息发送者
   */
  sender: 'USER' | 'ASSISTANT';

  /**
   * 消息创建时间
   */
  createdAt: string;

  /**
   * 高亮文本（可能与搜索词不同，用于语义搜索）
   */
  keywords: string[];
}
