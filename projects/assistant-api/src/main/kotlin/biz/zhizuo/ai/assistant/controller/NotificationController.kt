package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.NotificationResponse
import biz.zhizuo.ai.assistant.service.NotificationService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 消息控制器
 */
@RestController
@RequestMapping("/api/notifications")
@CrossOrigin(origins = ["*"])
class NotificationController(
    private val notificationService: NotificationService,
) {

    /**
     * 获取消息数据
     */
    @GetMapping
    fun getNotifications(): ResponseEntity<NotificationResponse> {
        return try {
            val response = notificationService.query()
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            ResponseEntity.status(500).body(null)
        }
    }
}
