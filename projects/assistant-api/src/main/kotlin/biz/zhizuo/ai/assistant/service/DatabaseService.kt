package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.config.DataInitializer
import biz.zhizuo.ai.assistant.repository.ChatMessageRepository
import biz.zhizuo.ai.assistant.repository.ChatSessionRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 数据库管理服务
 */
@Service
@Transactional
class DatabaseService(
    private val chatSessionRepository: ChatSessionRepository,
    private val chatMessageRepository: ChatMessageRepository,
    private val dataInitializer: DataInitializer,
) {

    /**
     * 重置数据库
     * 清空所有聊天相关数据并重新初始化演示数据
     */
    fun resetDatabase(): String {
        try {
            // 清空聊天相关数据
            chatMessageRepository.deleteAll()
            chatSessionRepository.deleteAll()

            // 初始化演示数据
            dataInitializer.run()

            return "数据库重置成功"
        } catch (e: Exception) {
            throw RuntimeException("数据库重置失败: ${e.message}", e)
        }
    }
}
