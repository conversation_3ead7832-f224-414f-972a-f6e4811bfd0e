package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天消息反馈实体
 */
@Entity
@Table(name = "chat_message_feedback")
@EntityListeners(AuditingEntityListener::class)
data class ChatMessageFeedback(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @OneToOne
    val message: ChatMessage,

    val rate: Int,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
