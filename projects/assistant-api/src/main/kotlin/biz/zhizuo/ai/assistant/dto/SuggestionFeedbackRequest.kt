package biz.zhizuo.ai.assistant.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for recording a user's selection of a suggestion.
 * This is used to track which suggestions are being utilized by users,
 * to improve future suggestion generation.
 */
data class SuggestionFeedbackRequest(
    @JsonProperty("isReportTemplate")
    val isReportTemplate: Boolean? = false, // Indicates if the suggestion was a report template

    val messageId: String, // The ID of the message in context of which the suggestion was provided
    val sessionId: String, // The ID of the session in which the suggestion was provided
    val suggestionId: String, // The unique identifier of the suggestion itself
    val suggestionText: String // The text content of the suggestion that was clicked
)
