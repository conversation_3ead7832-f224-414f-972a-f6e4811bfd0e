<mat-toolbar color="primary">
  <div class="toolbar-left">
    <button
        type="button"
        aria-label="Toggle sidenav"
        mat-icon-button
        (click)="onToggleSidenav()">
      <mat-icon svgIcon="menu" matButtonIcon aria-label="Side nav toggle icon"></mat-icon>
    </button>
    <div class="app-logo">
      <img ngSrc="/assets/logo.svg" priority="high" alt="应用Logo" width="32" height="32">
    </div>
    <span class="app-title">AI 智能助理</span>

    <!-- 搜索框组件将在这里引入 -->
    <ng-content select="[search-bar]"></ng-content>
  </div>

  <div class="toolbar-right">
    <!-- 移动设备上的直达功能按钮 -->
    <button
        *ngIf="isHandset"
        type="button"
        aria-label="直达功能"
        mat-icon-button
        (click)="onToggleMobileSearch()">
      <mat-icon svgIcon="smart-toy" matButtonIcon aria-label="直达功能图标"></mat-icon>
    </button>

    <button
        type="button"
        aria-label="Notifications"
        mat-icon-button
        routerLink="/notifications"
        [matBadge]="notificationCount"
        [matBadgeHidden]="notificationCount === 0"
        matBadgeColor="accent">
      <mat-icon svgIcon="notifications" matButtonIcon aria-label="Notifications icon"></mat-icon>
    </button>

    <button
        type="button"
        aria-label="Help"
        routerLink="/help"
        mat-icon-button>
      <mat-icon svgIcon="help" matButtonIcon aria-label="Help icon"></mat-icon>
    </button>

    <!-- 用户菜单组件将在这里引入 -->
    <ng-content select="[user-menu]"></ng-content>
  </div>
</mat-toolbar>
