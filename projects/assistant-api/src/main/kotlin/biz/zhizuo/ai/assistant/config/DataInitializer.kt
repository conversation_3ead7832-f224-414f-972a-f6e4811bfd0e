package biz.zhizuo.ai.assistant.config

import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.*
import org.slf4j.LoggerFactory
import org.springframework.boot.CommandLineRunner
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Component
import java.time.LocalDate

/**
 * 数据初始化器
 * 在应用启动时初始化演示数据
 */
@Component
class DataInitializer(
    private val userRepository: UserRepository,
    private val userProfileRepository: UserProfileRepository,
    private val userSettingsRepository: UserSettingsRepository,
    private val dashboardDataRepository: DashboardDataRepository,
    private val messageRepository: MessageRepository,
    private val favoriteRepository: FavoriteRepository,
    private val aboutInfoRepository: AboutInfoRepository,
    private val helpInfoRepository: HelpInfoRepository,
    private val chatSessionRepository: ChatSessionRepository,
    private val chatMessageRepository: ChatMessageRepository,
    private val passwordEncoder: PasswordEncoder,
) : CommandLineRunner {
    private val logger = LoggerFactory.getLogger(javaClass)
    override fun run(vararg args: String?) {
        initializeData()
    }

    private fun initializeData() {
        // 检查是否已经初始化过数据
        if (userRepository.count() > 0) {
            return
        }

        logger.info("开始初始化演示数据...")

        // 创建演示用户
        val demoUser = userRepository.save(
            User(
                username = "demo",
                email = "<EMAIL>",
                password = passwordEncoder.encode("123456")
            )
        )

        // 创建用户资料
        userProfileRepository.save(
            UserProfile(
                userId = demoUser.id!!,
                name = "演示用户",
                avatar = "assets/default-avatar.svg",
                email = demoUser.email,
                joinDate = LocalDate.now(),
                bio = "这是一个演示用户账户",
                postsCount = 12,
                followersCount = 156,
                followingCount = 89
            )
        )

        // 创建用户设置
        userSettingsRepository.save(
            UserSettings(
                userId = demoUser.id!!
            )
        )

        // 初始化仪表盘数据
        initializeDashboardData()

        // 初始化消息数据
        initializeNotificationData(demoUser.id!!)

        // 初始化收藏数据
        initializeFavoriteData(demoUser.id!!)

        // 初始化关于信息
        initializeAboutInfo()

        // 初始化帮助信息
        initializeHelpInfo()

        // 初始化聊天数据
        initializeChatData(demoUser)

        logger.info("演示数据初始化完成")
    }

    private fun initializeDashboardData() {
        val dashboardData = listOf(
            DashboardData(
                type = "stat",
                title = "总访问量",
                value = "12,345",
                icon = "visibility",
                color = "primary",
                sortOrder = 1
            ),
            DashboardData(
                type = "stat",
                title = "新用户",
                value = "234",
                icon = "person_add",
                color = "accent",
                sortOrder = 2
            ),
            DashboardData(
                type = "stat",
                title = "活跃用户",
                value = "1,567",
                icon = "people",
                color = "warn",
                sortOrder = 3
            ),
            DashboardData(
                type = "stat",
                title = "收入",
                value = "¥45,678",
                icon = "attach_money",
                color = "primary",
                sortOrder = 4
            ),

            DashboardData(
                type = "activity",
                title = "用户注册",
                description = "新用户 张三 注册了账户",
                value = "2分钟前",
                icon = "person_add",
                color = "primary",
                sortOrder = 1
            ),
            DashboardData(
                type = "activity",
                title = "订单完成",
                description = "订单 #12345 已完成支付",
                value = "5分钟前",
                icon = "shopping_cart",
                color = "accent",
                sortOrder = 2
            ),
            DashboardData(
                type = "activity",
                title = "系统更新",
                description = "系统已更新到 v2.1.0",
                value = "1小时前",
                icon = "system_update",
                color = "warn",
                sortOrder = 3
            )
        )

        dashboardDataRepository.saveAll(dashboardData)
    }

    private fun initializeNotificationData(userId: String) {
        val notifications = listOf(
            Notification(
                type = "notification",
                category = "system",
                title = "系统维护通知",
                content = "系统将于今晚22:00-24:00进行维护",
                isRead = false,
                icon = "info",
                color = "primary"
            ),
            Notification(
                type = "notification",
                category = "update",
                title = "功能更新",
                content = "新增了消息推送功能",
                isRead = false,
                icon = "new_releases",
                color = "accent"
            ),
            Notification(
                type = "notification",
                category = "security",
                title = "安全提醒",
                content = "检测到异常登录，请及时修改密码",
                isRead = true,
                icon = "security",
                color = "warn"
            ),

            Notification(
                userId = userId,
                type = "private",
                title = "欢迎消息",
                content = "欢迎使用我们的系统！",
                isRead = false,
                icon = "mail",
                color = "primary"
            ),
            Notification(
                userId = userId,
                type = "private",
                title = "账户验证",
                content = "您的账户已通过验证",
                isRead = true,
                icon = "verified",
                color = "accent"
            )
        )

        messageRepository.saveAll(notifications)
    }

    private fun initializeFavoriteData(userId: String) {
        val favorites = listOf(
            Favorite(
                userId = userId,
                type = "article",
                title = "Spring Boot 最佳实践",
                description = "详细介绍 Spring Boot 开发的最佳实践和常见问题解决方案",
                url = "https://example.com/spring-boot-best-practices",
                image = "assets/placeholder.svg",
                author = "张三",
                publishedDate = "2024-01-15",
                readTime = "10分钟"
            ),
            Favorite(
                userId = userId,
                type = "article",
                title = "Kotlin 协程详解",
                description = "深入理解 Kotlin 协程的工作原理和使用场景",
                url = "https://example.com/kotlin-coroutines",
                image = "assets/placeholder.svg",
                author = "李四",
                publishedDate = "2024-01-10",
                readTime = "15分钟"
            ),
            Favorite(
                userId = userId,
                type = "video",
                title = "Angular 18 新特性介绍",
                description = "全面介绍 Angular 18 的新特性和改进",
                url = "https://example.com/angular-18-features",
                image = "assets/placeholder.svg",
                author = "王五",
                publishedDate = "2024-01-12",
                duration = "25:30",
                viewCount = "1.2K"
            )
        )

        favoriteRepository.saveAll(favorites)
    }

    private fun initializeAboutInfo() {
        val aboutInfo = listOf(
            AboutInfo(
                type = "company",
                title = "北京智座科技发展中心（工作室）",
                description = "旨在帮助小微企业导入 AI 能力",
                content = "成立于2023年10月。", // 将成立时间放在 content
                sortOrder = 1
            ),
            // 企业文化信息已移除

            AboutInfo(
                type = "team",
                title = "汪志成",
                description = "资深工程师，Google Developer Expert", // 职位和荣誉
                content = "27年开发经验，前 ThoughtWorks 专家级咨询师。", // 经验和背景
                image = "assets/user-avatar.svg", // 保留默认头像
                // position 字段移除，与前端 AboutInfoDto 保持一致性
                sortOrder = 1
            ),
            // 任春娜信息已移除

            AboutInfo(
                type = "milestone",
                title = "2023年10月 - 工作室成立",
                description = "北京智座科技发展中心（工作室）成立，旨在帮助小微企业导入 AI 能力。",
                dateValue = "2023-10",
                sortOrder = 1
            )
            // 首个产品发布信息已移除
        )

        aboutInfoRepository.saveAll(aboutInfo)
    }

    private fun initializeHelpInfo() {
        val helpInfo = listOf(
            HelpInfo(type = "category", title = "账户管理", icon = "account_circle", sortOrder = 1),
            HelpInfo(type = "category", title = "功能使用", icon = "help", sortOrder = 2),
            HelpInfo(type = "category", title = "技术支持", icon = "support", sortOrder = 3),

            HelpInfo(
                type = "faq",
                title = "如何修改密码？",
                content = "进入设置页面，点击安全设置，选择修改密码",
                category = "账户管理",
                sortOrder = 1
            ),
            HelpInfo(
                type = "faq",
                title = "如何绑定邮箱？",
                content = "在个人资料页面可以绑定和修改邮箱地址",
                category = "账户管理",
                sortOrder = 2
            ),
            HelpInfo(
                type = "faq",
                title = "如何使用收藏功能？",
                content = "在任何页面点击收藏按钮即可收藏内容",
                category = "功能使用",
                sortOrder = 3
            ),
            HelpInfo(
                type = "faq",
                title = "遇到问题如何联系客服？",
                content = "可以通过联系我们页面提交问题或发送邮件",
                category = "技术支持",
                sortOrder = 4
            )
        )

        helpInfoRepository.saveAll(helpInfo)
    }

    private fun initializeChatData(user: User) {
        // 创建演示会话
        val session1 = chatSessionRepository.save(
            ChatSession(
                user = user,
                name = "AI助手介绍",
                pinned = true
            )
        )

        val session2 = chatSessionRepository.save(
            ChatSession(
                user = user,
                name = "编程问题讨论",
                pinned = false
            )
        )

        val session3 = chatSessionRepository.save(
            ChatSession(
                user = user,
                name = "日常对话",
                pinned = false
            )
        )

        // 为第一个会话创建演示消息
        session1.id?.let { sessionId ->
            val userMessage1 = chatMessageRepository.save(
                ChatMessage(
                    session = session1,
                    user = user,
                    role = MessageRole.USER,
                    content = "你好，请介绍一下你自己。"
                )
            )

            val assistantMessage1 = chatMessageRepository.save(
                ChatMessage(
                    session = session1,
                    user = user,
                    role = MessageRole.ASSISTANT,
                    content = "你好！我是智能助手，一个基于人工智能技术的对话系统。我可以帮助您解答问题、提供信息、协助完成各种任务。我具备以下能力：\n\n1. **知识问答**：回答各种领域的问题\n2. **文本处理**：帮助编写、修改、总结文本\n3. **编程协助**：提供代码建议和调试帮助\n4. **创意支持**：协助创作和头脑风暴\n5. **学习辅导**：解释概念和提供学习建议\n\n有什么我可以帮助您的吗？",
                    assistantName = "智能助手",
                    replyTo = userMessage1,
                )
            )

            val userMessage2 = chatMessageRepository.save(
                ChatMessage(
                    session = session1,
                    user = user,
                    role = MessageRole.USER,
                    content = "你能帮我写代码吗？",
                    replyTo = assistantMessage1,
                )
            )

            chatMessageRepository.save(
                ChatMessage(
                    session = session1,
                    user = user,
                    role = MessageRole.ASSISTANT,
                    content = "当然可以！我可以帮助您编写各种编程语言的代码，包括但不限于：\n\n- **Web开发**：HTML, CSS, JavaScript, TypeScript, React, Angular, Vue.js\n- **后端开发**：Java, Python, Node.js, C#, Go, Kotlin\n- **移动开发**：Swift, Kotlin, React Native, Flutter\n- **数据科学**：Python (pandas, numpy, matplotlib), R, SQL\n- **系统编程**：C, C++, Rust\n\n请告诉我您需要什么样的代码，我会尽力帮助您！",
                    assistantName = "智能助手",
                    replyTo = userMessage2,
                )
            )
        }

        // 为第二个会话创建演示消息
        session2.id?.let { _ -> // sessionId variable is not used inside this block
            val userMessage = chatMessageRepository.save(
                ChatMessage(
                    session = session2, // Corrected: use session2
                    user = user,
                    role = MessageRole.USER,
                    content = "如何在Spring Boot中实现JWT认证？"
                )
            )

            chatMessageRepository.save(
                ChatMessage(
                    session = session2, // Corrected: use session2
                    user = user,
                    role = MessageRole.ASSISTANT,
                    content = "在Spring Boot中实现JWT认证需要以下几个步骤：\n\n## 1. 添加依赖\n```xml\n<dependency>\n    <groupId>io.jsonwebtoken</groupId>\n    <artifactId>jjwt-api</artifactId>\n    <version>0.11.5</version>\n</dependency>\n```\n\n## 2. 创建JWT工具类\n```kotlin\n@Component\nclass JwtTokenProvider {\n    private val secretKey = \"your-secret-key\"\n    private val validityInMilliseconds = 3600000 // 1小时\n    \n    fun createToken(username: String): String {\n        // JWT生成逻辑\n    }\n    \n    fun validateToken(token: String): Boolean {\n        // JWT验证逻辑\n    }\n}\n```\n\n## 3. 配置Security\n需要配置Spring Security来使用JWT过滤器。\n\n需要我详细解释某个步骤吗？",
                    assistantName = "智能助手",
                    replyTo = userMessage,
                )
            )
        }

        // 更新会话的最后消息时间
        val now = java.time.LocalDateTime.now()
        session1.id?.let { chatSessionRepository.save(session1.copy(lastMessageAt = now.minusHours(1))) }
        session2.id?.let { chatSessionRepository.save(session2.copy(lastMessageAt = now.minusMinutes(30))) }
        session3.id?.let { chatSessionRepository.save(session3.copy(lastMessageAt = now.minusDays(1))) }
    }
}
