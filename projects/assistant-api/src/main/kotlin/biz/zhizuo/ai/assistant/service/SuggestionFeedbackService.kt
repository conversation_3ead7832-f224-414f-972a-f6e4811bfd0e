package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.SuggestionFeedbackRequest
import biz.zhizuo.ai.assistant.entity.SuggestionFeedback
import biz.zhizuo.ai.assistant.repository.SuggestionFeedbackRepository
import biz.zhizuo.ai.assistant.security.UserPrincipal
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SuggestionFeedbackService(
    private val suggestionFeedbackRepository: SuggestionFeedbackRepository
) {

    private val logger = LoggerFactory.getLogger(SuggestionFeedbackService::class.java)

    @Transactional
    fun submitFeedback(request: SuggestionFeedbackRequest, userPrincipal: UserPrincipal?) {
        logger.info(
            "Recording suggestion selection by user: {}. MessageID: {}, SessionID: {}, SuggestionID: {}, SuggestionText: {}, IsReportTemplate: {}",
            userPrincipal?.id?.toString() ?: "Anonymous/System", // Clarify if user can be null
            request.messageId,
            request.sessionId,
            request.suggestionId,
            request.suggestionText,
            request.isReportTemplate ?: false // DTO allows null, entity expects non-null, defaulting here
        )

        val suggestionSelectionRecord = SuggestionFeedback( // Consider renaming 'feedback' variable for clarity
            suggestionText = request.suggestionText,
            isReportTemplate = request.isReportTemplate ?: false, // Default to false if null from DTO
            messageId = request.messageId,
            sessionId = request.sessionId,
            suggestionId = request.suggestionId,
            userId = userPrincipal?.id?.toString()
        )

        suggestionFeedbackRepository.save(suggestionSelectionRecord)

        logger.info("Suggestion selection record saved with ID: {}", suggestionSelectionRecord.id)
    }
}
