import { Injectable } from '@angular/core';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';
import { ChatSessionApi } from './chat-session-api.service';
import { ChatSession } from './chat-session';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LocalStateHolder } from './last-session-holder.service';
import { distinctUntilChanged } from 'rxjs/operators';
import { AuthService } from '../../../core/services/auth.service';

/**
 * 会话服务
 * 负责管理聊天会话
 */
@Injectable({
  providedIn: 'root',
})
export class ChatSessionService {
  constructor(
    private sessionApi: ChatSessionApi,
    private authService: AuthService,
    private localStateHolder: LocalStateHolder,
  ) {
    // 监听认证状态变化
    this.authService.currentUser$
      .pipe(takeUntilDestroyed())
      .subscribe(async (user) => {
        if (user) {
          // 用户已登录，加载会话列表并订阅事件
          await this.loadSessions();

          this.restoreLastSelection(); // 会话列表的 setter 中已调用
        } else {
          // 清空会话列表
          this.sessions = [];
        }
      });
  }

  // 会话相关方法实现
  private _sessions: ReadonlyArray<ChatSession> = [];

  async loadSessions() {
    this.sessions = await firstValueFrom(this.sessionApi.query());
  }

  // 获取会话列表
  get sessions(): ReadonlyArray<ChatSession> {
    return this._sessions;
  }

  // 设置会话列表
  private set sessions(value: ReadonlyArray<ChatSession>) {
    this._sessions = value;
    this.restoreLastSelection();
    // 更新置顶和非置顶列表
    this.updatePinnedLists();
  }

  async togglePin(session: ChatSession) {
    return this.update(session.id, { pinned: !session.pinned });
  }

  private _pinnedList: ReadonlyArray<ChatSession> = [];
  get pinnedList(): ReadonlyArray<ChatSession> {
    return this._pinnedList;
  }

  private _unpinnedList: ReadonlyArray<ChatSession> = [];

  get unpinnedList(): ReadonlyArray<ChatSession> {
    return this._unpinnedList;
  }

  // 更新置顶和非置顶列表
  private updatePinnedLists(): void {
    this._pinnedList = this._sessions.filter((it) => it.pinned);
    this._unpinnedList = this._sessions.filter((it) => !it.pinned);
  }

  private selection$$ = new BehaviorSubject<ChatSession | undefined>(undefined);

  private _selection$ = this.selection$$.pipe(
    takeUntilDestroyed(),
    distinctUntilChanged((v1, v2) => v1?.id === v2?.id),
  );

  get selection$(): Observable<ChatSession | undefined> {
    return this._selection$;
  }

  get selection(): ChatSession | undefined {
    return this.selection$$.value;
  }

  set selection(value: ChatSession | undefined) {
    this.selection$$.next(value);

    // 保存最后活跃的会话ID
    const sessionId = value?.id;
    if (sessionId) {
      this.localStateHolder.saveLastSessionId(sessionId);
    } else {
      this.localStateHolder.clearLastSessionId();
    }
  }

  isActive(sessionId: string): boolean {
    return this.selection?.id === sessionId;
  }

  /**
   * 根据ID选择会话
   * @param sessionId 要选择的会话ID
   */
  selectById(sessionId: string | undefined): void {
    const lastSession = this.sessions[this.sessions.length - 1];
    if (!sessionId) {
      this.selection = lastSession;
      return;
    }

    // 查找完整的会话对象
    this.selection = this.getById(sessionId) ?? lastSession;
  }

  /**
   * 恢复选中上次的会话
   * 如果有上次活跃的会话ID，则选择该会话
   * 如果该会话不存在，则选择最后一个会话
   */
  private restoreLastSelection(): void {
    // 如果没有会话，直接返回
    if (this.sessions.length === 0) {
      return;
    }

    // 获取上次活跃的会话ID
    const lastSessionId = this.localStateHolder.getLastSessionId();

    this.selectById(lastSessionId);
  }

  /**
   * 根据ID获取会话
   * @param sessionId 会话ID
   * @returns 会话对象或undefined
   */
  getById(sessionId: string): ChatSession | undefined {
    return this._sessions.find((it) => it.id === sessionId);
  }

  /**
   * 创建新会话
   * @param name 会话名称
   * @returns 创建的会话对象的Promise
   */
  async create(name: string): Promise<ChatSession> {
    const session = await firstValueFrom(this.sessionApi.create({ name }));

    // 更新内存中的会话缓存
    this.sessions = [...this.sessions, session];

    this.selectById(session.id);

    return session;
  }

  /**
   * 更新会话
   * @param sessionId 会话ID
   * @param updates 要更新的字段
   * @returns 更新后的会话对象的Promise
   */
  async update(
    sessionId: string,
    updates: Partial<ChatSession>,
  ): Promise<ChatSession> {
    // 首先检查会话是否存在
    const session = this.getById(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }

    // 调用API更新会话
    const updatedSession = await firstValueFrom(
      this.sessionApi.update(sessionId, updates),
    );

    // 更新内存中的会话缓存
    Object.assign(session, updatedSession);

    return updatedSession;
  }

  /**
   * 删除会话
   * @param sessionId 要删除的会话ID
   * @returns 删除操作的Promise
   */
  async delete(sessionId: string): Promise<void> {
    // 首先检查会话是否存在
    if (!this.getById(sessionId)) {
      return;
    }

    // 调用API删除会话
    await firstValueFrom(this.sessionApi.delete(sessionId));

    // 更新内存中的会话缓存
    this.sessions = this.sessions.filter((s) => s.id !== sessionId);

    // 如果删除的是当前选中的会话，则自动选中另一个会话
    if (this.selection?.id === sessionId) {
      const sessions = this.sessions.filter((s) => s.id !== sessionId);
      if (sessions.length > 0) {
        this.selection = sessions[0];
      } else {
        this.selection = undefined;
      }
    }
  }
}
