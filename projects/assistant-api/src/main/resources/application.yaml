server:
  port: 18081
  http2:
    enabled: true

spring:
  application:
    name: assistant-api
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    open-in-view: false
  session:
    jdbc:
      initialize-schema: always
  jackson:
    default-property-inclusion: NON_NULL
  ai:
    vertex:
      ai:
        gemini:
          project-id: ralph-gde
          location: us-central1
          chat:
            options:
              model: gemini-2.0-flash-lite
              temperature: 0
        embedding:
          project-id: ralph-gde
          location: us-central1
    vectorstore:
      pgvector:
        initialize-schema: true

  datasource:
    url: *********************************************

logging:
  charset:
    console: UTF-8
    file: UTF-8

# 应用配置
app:
  jwt:
    secret: AStrongAndSecureSecretKeyForJWTGenerationMustBeAtLeast64BytesLong123
    expiration: 86400000 # 24小时（毫秒）
    remember-me-expiration: 2592000000 # 30天（毫秒）
