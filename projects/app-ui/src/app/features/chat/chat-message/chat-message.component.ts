import {
  AfterViewInit,
  Component,
  computed,
  ElementRef,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { ActivatedRoute } from '@angular/router';
import { FeedbackDialogComponent } from '../feedback-dialog/feedback-dialog.component';
import { ChatGenerationStatusComponent } from '../chat-generation-status/chat-generation-status.component';
import {
  ConfirmDialogComponent,
  ConfirmDialogData,
} from '../confirm-dialog/confirm-dialog.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ChatMessageService } from '../_services/chat-message.service';
import { ChatMessage } from '../_services/chat-message';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {MarkdownComponent} from '../../../shared/markdown/markdown.component';
import {ClipboardService} from '../../../shared/utils/clipboard.service';

@Component({
  selector: 'app-chat-message',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatDialogModule,
    MatMenuModule,
    MarkdownComponent,
    ChatGenerationStatusComponent,
  ],
  templateUrl: './chat-message.component.html',
  styleUrl: './chat-message.component.scss',
})
export class ChatMessageComponent implements AfterViewInit, OnInit {
  // 高亮关键词
  private highlightKeyword?: string;

  // 回复消息引用
  protected replyToMessage?: ChatMessage;

  constructor(
    private clipboardService: ClipboardService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    protected messageService: ChatMessageService,
    private route: ActivatedRoute,
    private el: ElementRef
  ) {
    // 监听 URL 参数变化，处理消息高亮
    this.route.queryParams.pipe(takeUntilDestroyed()).subscribe((params) => {
      // 清除当前消息的高亮
      this.clearHighlightContent();

      if (params['messageId'] === this.message?.id) {
        if (params['keywords']) {
          // 处理关键词数组或字符串
          const keywords = Array.isArray(params['keywords'])
            ? params['keywords']
            : params['keywords'].split(',');
          this.highlightKeyword = keywords.join('|');
        } else if (params['highlight']) {
          this.highlightKeyword = params['highlight'];
        } else {
          this.highlightKeyword = undefined;
        }
      } else {
        this.highlightKeyword = undefined;
      }
    });
  }

  @Input() message!: ChatMessage;

  isUser = computed(() => this.message.role === 'USER');
  isAssistant = computed(() => this.message.role === 'ASSISTANT');

  ngOnInit(): void {
    // 如果是助理消息，获取对应的用户消息
    if (this.isAssistant() && this.message.replyToId) {
      try {
        this.replyToMessage = this.messageService.getById(this.message.replyToId);
      } catch (error) {
        console.error('获取回复消息失败:', error);
      }
    }
  }

  ngAfterViewInit(): void {
    // 如果有高亮关键词，尝试高亮消息内容
    if (this.highlightKeyword) {
      this.highlightContent();
    }
  }

  /**
   * 检查是否有多个版本
   * @returns 是否有多个版本
   */
  protected hasMultipleVersions(): boolean {
    return !!(this.replyToMessage?.replyIds && this.replyToMessage.replyIds.length > 1);
  }

  /**
   * 获取前N个版本ID
   * @param count 要获取的版本数量
   * @returns 版本ID数组
   */
  protected getFirstNVersions(count: number): string[] {
    if (!this.replyToMessage?.replyIds) return [];
    return this.replyToMessage.replyIds.slice(0, count);
  }

  /**
   * 获取剩余的版本ID（跳过前N个）
   * @param skip 要跳过的版本数量
   * @returns 版本ID数组
   */
  protected getRemainingVersions(skip: number): string[] {
    if (!this.replyToMessage?.replyIds) return [];
    return this.replyToMessage.replyIds.slice(skip);
  }

  /**
   * 切换到指定版本
   * @param replyId 要切换到的版本ID
   */
  protected async onSwitchVersion(replyId: string): Promise<void> {
    if (!this.replyToMessage) return;

    try {
      // 显示加载提示
      this.snackBar.open('正在切换版本...', undefined, {
        duration: undefined,
      });

      // 调用切换回复方法
      await this.messageService.switchActiveReply(this.replyToMessage.id, replyId);

      // 关闭加载提示
      this.snackBar.dismiss();

      this.snackBar.open('已切换到其他回复版本', '关闭', {
        duration: 2000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom',
      });
    } catch (error) {
      console.error('切换版本失败:', error);
      this.snackBar.open('切换版本失败', '关闭', {
        duration: 3000,
      });
    }
  }

  /**
   * 清除消息内容中的高亮标记
   */
  private clearHighlightContent(): void {
    try {
      // 查找消息内容元素
      const contentElement = this.el.nativeElement.querySelector(
        '.message-body'
      ) as HTMLElement;
      if (!contentElement) return;

      const markdownContent = contentElement.querySelector('.markdown-content');
      if (markdownContent) {
        // 将所有高亮标记替换回原文本
        const marks = markdownContent.querySelectorAll('mark.search-highlight');
        marks.forEach((mark) => {
          const text = mark.textContent || '';
          const textNode = document.createTextNode(text);
          mark.parentNode?.replaceChild(textNode, mark);
        });
      }
    } catch (err) {
      console.error('清除高亮标记时出错:', err);
    }
  }

  /**
   * 高亮消息内容中的关键词
   */
  private highlightContent(): void {
    if (!this.highlightKeyword) return;

    try {
      // 查找消息内容元素
      const contentElement =
        this.el.nativeElement.querySelector('.message-body');
      if (!contentElement) return;

      // 将文本包裹在高亮标记中
      setTimeout(() => {
        const markdownContent =
          contentElement.querySelector('.markdown-content');
        if (markdownContent) {
          const html = markdownContent.innerHTML;
          // 使用正则表达式支持多关键词高亮
          const regex = new RegExp(`(${this.highlightKeyword})`, 'gi');
          markdownContent.innerHTML = html.replace(
            regex,
            (match: string) => `<mark class="search-highlight">${match}</mark>`
          );
        }
      }, 100); // 等待渲染完成
    } catch (err) {
      console.error('高亮关键词时出错:', err);
    }
  }

  /**
   * 复制消息内容
   */
  async onCopy(): Promise<void> {
    await this.clipboardService.copyToClipboard(this.message.content);
    this.snackBar.open('已复制到剪贴板', '关闭', {
      duration: 2000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom',
    });
  }

  /**
   * 点赞消息
   */
  async onLike(): Promise<void> {
    try {
      await this.messageService.like(this.message.id);
      this.snackBar.open('感谢您的反馈！', '关闭', {
        duration: 2000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom',
      });
    } catch (error) {
      console.error('点赞消息失败:', error);
      this.snackBar.open('操作失败，请重试', '关闭', {
        duration: 3000,
      });
    }
  }

  /**
   * 点踩消息
   */
  async onDislike(): Promise<void> {
    const dialogRef = this.dialog.open(FeedbackDialogComponent, {
      width: '500px',
    });

    dialogRef.afterClosed().subscribe(async (feedback) => {
      if (feedback) {
        try {
          // 如果有反馈内容，添加反馈
          await this.messageService.addFeedback(this.message.id, {
            rate: 3,
            content: feedback,
          });

          await this.messageService.dislike(this.message.id);

          this.snackBar.open('感谢您的反馈！', '关闭', {
            duration: 2000,
            horizontalPosition: 'center',
            verticalPosition: 'bottom',
          });
        } catch (error) {
          console.error('添加反馈失败:', error);
          this.snackBar.open('操作失败，请重试', '关闭', {
            duration: 3000,
          });
        }
      }
    });
  }

  /**
   * 重新生成消息
   */
  async onRegenerate(): Promise<void> {
    if (!this.isAssistant()) return;

    // 检查消息是否有回复对象
    if (!this.message.replyToId) {
      this.snackBar.open('无法重新生成没有回复对象的助理消息', '关闭', {
        duration: 3000,
      });
      return;
    }

    // 显示加载提示
    this.snackBar.open('正在重新生成回复...', undefined, {
      duration: undefined,
    });

    try {
      // 调用重新生成方法
      await this.messageService.regenerate(this.message);

      // 关闭加载提示
      this.snackBar.dismiss();
    } catch (error) {
      console.error('重新生成消息失败:', error);
      this.snackBar.open('重新生成失败', '关闭', {
        duration: 3000,
      });
    }
  }

  /**
   * 编辑并重新发送
   */
  onEditAndResend(): void {
    // 这个操作需要父组件处理，因为它涉及到输入框的操作
    this.messageService.editAndResend(this.message.id);
  }

  /**
   * 删除消息
   */
  async onDelete(): Promise<void> {
    const dialogRef = this.dialog.open<
      ConfirmDialogComponent,
      ConfirmDialogData,
      boolean
    >(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: '删除消息',
        message:
          '确定要删除这条消息吗？如果是用户消息，将同时删除其直接回复的助理消息。',
        confirmText: '删除',
        cancelText: '取消',
      },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result) {
        try {
          // 显示加载提示
          this.snackBar.open('正在删除消息...', undefined, {
            duration: undefined,
          });

          // 调用删除消息方法
          await this.messageService.delete(this.message.id);

          // 关闭加载提示
          this.snackBar.dismiss();

          // 删除消息后尊重用户滚动行为
          this.snackBar.open('消息已删除', '关闭', {
            duration: 2000,
            horizontalPosition: 'center',
            verticalPosition: 'bottom',
          });
        } catch (error) {
          console.error(`删除消息 ${this.message.id} 时发生异常:`, error);
          this.snackBar.open('删除消息时发生错误', '关闭', {
            duration: 3000,
          });
        }
      }
    });
  }

  /**
   * 切换用户消息的活跃回复
   * @param replyId 要激活的助理消息ID
   */
  async onSwitchReply(replyId: string): Promise<void> {
    if (!this.isUser()) return;

    try {
      // 显示加载提示
      this.snackBar.open('正在切换回复...', undefined, {
        duration: undefined,
      });

      // 调用切换回复方法
      await this.messageService.switchActiveReply(this.message.id, replyId);

      // 关闭加载提示
      this.snackBar.dismiss();

      this.snackBar.open('已切换到其他回复版本', '关闭', {
        duration: 2000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom',
      });
    } catch (error) {
      console.error('切换回复失败:', error);
      this.snackBar.open('切换回复失败', '关闭', {
        duration: 3000,
      });
    }
  }

  /**
   * 检查是否为图片文件
   */
  isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(filename: string): string {
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1] : '';
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    }
  }
}
