.generation-status-container {
  margin: 0 0 12px 0; /* 与 Gemini 保持一致的边距 */
  border-radius: 16px;
  background-color: rgba(232, 240, 254, 0.4); // Gemini的浅蓝色背景
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-block;
  min-width: 180px; // 更紧凑的宽度
  max-width: 90%;
  border: 1px solid rgba(232, 240, 254, 0.8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); // 添加轻微阴影

  &.expanded {
    display: block;
  }

  &.completed {
    background-color: rgba(232, 240, 254, 0.2);
    border: 1px solid rgba(232, 240, 254, 0.4);
  }
}

.generation-status-header {
  display: flex;
  align-items: center;
  padding: 8px 12px; // 更紧凑的内边距
  cursor: pointer;
  position: relative;
  font-weight: 500;
  border-radius: 16px; // 保持圆角

  &:hover {
    background-color: rgba(232, 240, 254, 0.6);
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-right: 12px;
}

// Gemini风格的脉动点
.pulse-dots {
  display: flex;
  align-items: center;

  .dot {
    width: 4px; // 更小的点
    height: 4px; // 更小的点
    border-radius: 50%;
    background-color: #4285F4; // Google蓝
    margin-right: 3px; // 更紧凑的间距
    animation: pulse 1.2s infinite; // 更快的动画

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
      margin-right: 0;
    }
  }
}

// Gemini图标
.status-icon {
  margin-right: 12px;

  .gemini-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #4285F4;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: white;
    }
  }
}

.status-text {
  flex: 1;
  font-size: 13px; // 更小的字体
  color: #202124; // Gemini文本颜色
  font-weight: 500;
  letter-spacing: 0.01em; // 增加字间距
  white-space: nowrap;
}

.toggle-button {
  color: #5f6368; // Gemini次要文本颜色
  display: flex;
  align-items: center;
  justify-content: center;

}

.generation-status-details {
  padding: 0 16px 12px; // 减小底部填充
  width: auto;
  display: block; // 改为块级显示
  margin-left: 24px; // 缩进以对齐Gemini风格
  max-width: calc(100% - 40px); // 限制最大宽度
}

// 阶段分组样式
.phase-groups {
  display: flex;
  flex-direction: column;
  gap: 8px; // 减小分组间的空隙
}

.phase-group {
  margin-bottom: 8px; // 减小分组间的距离

  &:last-child {
    margin-bottom: 0;
  }
}

.phase-header {
  margin-bottom: 4px; // 减小标题和内容的距离
  padding-bottom: 2px;
  border-bottom: 1px solid rgba(66, 133, 244, 0.1); // 减少分隔线的显著度
}

.phase-name {
  font-size: 12px; // 减小字体
  font-weight: 400; // 减少字重
  color: #5f6368; // 使用次要颜色，不那么显著
  letter-spacing: 0.01em;
}

.no-steps-message {
  color: #5f6368;
  font-size: 13px;
  font-style: italic;
  margin: 12px 0;
  text-align: center;
}

.step-item,
.step-item {
  margin: 12px 0;
  opacity: 0.6;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    opacity: 1;
    font-weight: 500;
  }

  &.completed {
    opacity: 0.6;
    color: #5f6368; // 已完成的步骤显示为灰色
  }
}

.step-item {
  display: flex;
  align-items: center;
  margin: 6px 0; // 减小步骤间的距离
}

.step-item {
  .step-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .step-name {
    position: relative;
    font-size: 12px; // 更小的字体
    font-weight: 400; // 减少字重

    .step-duration {
      position: absolute;
      left: 0;
      top: 100%;
      font-size: 10px; // 更小的字体
      color: #5f6368;
      opacity: 0.7;
    }
  }

  .step-output {
    margin-left: 20px;
    margin-top: 2px; // 减小上边距
    padding: 6px 10px; // 减小内边距
    background-color: rgba(232, 240, 254, 0.2); // 更淡的背景
    border-radius: 6px; // 减小圆角
    font-size: 12px; // 更小的字体
    line-height: 1.4;
    color: #5f6368;
    max-width: 100%; // 允许占据全宽
    white-space: pre-wrap;
    border-left: 1px solid rgba(66, 133, 244, 0.2); // 减少边框显著度

    .active & {
      color: #202124;
      border-left-color: rgba(66, 133, 244, 0.5); // 减少活动状态下的边框显著度
    }
  }
}

.step-dot {
  width: 6px; // 减小点的大小
  height: 6px; // 减小点的大小
  border-radius: 50%;
  background-color: #5f6368; // Gemini次要颜色
  margin-right: 8px; // 减小右边距

  .active & {
    background-color: #4285F4; // Google蓝
  }

  .completed & {
    background-color: #5f6368; // 已完成的步骤点显示为灰色
  }
}

.step-text {
  font-size: 14px;
  color: #5f6368; // Gemini次要文本颜色

  .active & {
    color: #202124; // Gemini主要文本颜色
  }

  .completed & {
    color: #5f6368; // 已完成的步骤文本显示为灰色
  }
}

// 保留脉动动画效果
@keyframes pulse {
  0% {
    transform: scale(0.7);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(0.7);
    opacity: 0.4;
  }
}
