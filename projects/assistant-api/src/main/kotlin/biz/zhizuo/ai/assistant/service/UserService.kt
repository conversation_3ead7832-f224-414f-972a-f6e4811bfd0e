package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.entity.User
import biz.zhizuo.ai.assistant.repository.UserRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true) // Default to read-only for query methods
class UserService(
    private val userRepository: UserRepository
) {
    /**
     * 获取用户实体，如果找不到则抛出异常。
     * 其他服务通常期望用户存在，如果不存在则应中断操作。
     */
    fun getUserById(userId: String): User {
        return userRepository.findByIdOrNull(userId) ?: throw UserNotFoundException(userId)
    }
}
