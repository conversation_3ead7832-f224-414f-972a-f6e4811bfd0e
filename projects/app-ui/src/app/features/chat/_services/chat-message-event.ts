import { ChatMessage } from './chat-message';
import { ChatMessageGenerationStep } from './chat-message-generation-step';

/**
 * 消息事件基础接口
 */
export interface BaseChatMessageEvent {
  type: string;
  messageId: string;
}

/**
 * 消息生成内容事件
 */
export interface MessageStreamingContentEvent extends BaseChatMessageEvent {
  type: 'streaming-content';
  text: string;
  index: number;
}

/**
 * 消息生成阶段事件
 */
export interface MessageGenerationStepEvent extends BaseChatMessageEvent {
  type: 'generation-steps';
  steps: ChatMessageGenerationStep[];
}

/**
 * 消息生成完成事件
 */
export interface MessageGenerationCompleteEvent extends BaseChatMessageEvent {
  type: 'generation-complete';
  message: ChatMessage;
}

/**
 * 领域适配事件
 */
export interface DomainAdaptationEvent extends BaseChatMessageEvent {
  type: 'domain-adaptation';
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  result?: {
    detectedDomain: string;
    confidenceScore: number;
    executionPlan: any;
    reasoning?: string;
  };
  message?: string;
}

/**
 * 执行计划事件
 */
export interface ExecutionPlanEvent extends BaseChatMessageEvent {
  type: 'execution-plan';
  executionContext: any;
}

/**
 * 执行更新事件
 */
export interface ExecutionUpdateEvent extends BaseChatMessageEvent {
  type: 'execution-update';
  executionContext: any;
  eventType: string;
  currentStep?: any;
  message?: string;
}

/**
 * 打字效果事件
 */
export interface TypingEffectEvent extends BaseChatMessageEvent {
  type: 'typing-effect';
  text: string;
  chunkIndex: number;
  isComplete: boolean;
  timestamp: string;
}

/**
 * 消息事件类型
 */
export type ChatMessageEvent =
  | MessageStreamingContentEvent
  | MessageGenerationStepEvent
  | MessageGenerationCompleteEvent
  | DomainAdaptationEvent
  | ExecutionPlanEvent
  | ExecutionUpdateEvent
  | TypingEffectEvent;
