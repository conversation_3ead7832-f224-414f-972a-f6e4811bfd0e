/**
 * 执行计划接口
 */
export interface ExecutionPlan {
  id: string;
  name: string;
  description?: string;
  domain: string;
  isActive: boolean;
  priorityOrder: number;
  steps: ExecutionPlanStep[];
}

/**
 * 执行计划步骤接口
 */
export interface ExecutionPlanStep {
  id: string;
  name: string;
  description?: string;
  stepOrder: number;
  modelName?: string;
  expectedDurationSeconds?: number;
  isParallel: boolean;
}

/**
 * 消息执行上下文接口
 */
export interface MessageExecutionContext {
  id: string;
  messageId: string;
  executionPlan: ExecutionPlan;
  detectedDomain: string;
  confidenceScore: number;
  status: ExecutionStatus;
  currentStepOrder: number;
  startedAt?: Date;
  completedAt?: Date;
  stepExecutions: StepExecution[];
}

/**
 * 步骤执行记录接口
 */
export interface StepExecution {
  id: string;
  executionPlanStep: ExecutionPlanStep;
  status: StepStatus;
  startedAt?: Date;
  completedAt?: Date;
  durationSeconds?: number;
  output?: string;
  errorMessage?: string;
}

/**
 * 执行状态枚举
 */
export type ExecutionStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

/**
 * 步骤状态枚举
 */
export type StepStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

/**
 * 领域适配结果接口
 */
export interface DomainAdaptationResult {
  detectedDomain: string;
  confidenceScore: number;
  executionPlan: ExecutionPlan;
  reasoning?: string;
}
