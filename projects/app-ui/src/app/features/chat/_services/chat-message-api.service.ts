import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ChatMessageFeedback} from './chat-message-feedback';
import {ChatMessageGenerationStep} from './chat-message-generation-step';
import {Attachment} from './attachment';
import {ChatMessage} from './chat-message';
import {ChatMessageEvent} from './chat-message-event';
import {environment} from '../../../../environments/environment';
import {TokenHolder} from '../../../core/services/token-holder.service';

/**
 * 消息API请求参数
 */
export interface CreateUserMessageRequest {
  sessionId: string;
  content: string;
  attachments?: Attachment[];
  replyToId?: string;
}

export interface CreateUserMessageResponse {
  userMessage: ChatMessage;
  assistantMessage: ChatMessage;
}

export interface UpdateMessageRequest {
  content?: string;
  liked?: boolean;
  disliked?: boolean;
  feedback?: ChatMessageFeedback;
  activeReplyId?: string;
  steps?: ChatMessageGenerationStep[];
}

/**
 * 消息API服务
 */
@Injectable({providedIn: 'root'})
export class ChatMessageApi {
  private apiUrl = `${environment.apiBaseUrl}/messages`;

  constructor(private http: HttpClient, private tokenHolder: TokenHolder) {
  }

  /**
   * 获取会话中的所有消息
   */
  queryBySessionId(sessionId: string): Observable<ChatMessage[]> {
    return this.http.get<ChatMessage[]>(
        `${this.apiUrl}?sessionId=${sessionId}`,
    );
  }

  /**
   * 获取单个消息
   */
  fetch(messageId: string): Observable<ChatMessage> {
    return this.http.get<ChatMessage>(`${this.apiUrl}/${messageId}`);
  }

  /**
   * 创建用户消息
   */
  create(
      request: CreateUserMessageRequest,
  ): Observable<CreateUserMessageResponse> {
    return this.http.post<CreateUserMessageResponse>(
        `${this.apiUrl}/user`,
        request,
    );
  }

  /**
   * 更新消息
   */
  update(
      messageId: string,
      request: UpdateMessageRequest,
  ): Observable<ChatMessage> {
    return this.http.patch<ChatMessage>(`${this.apiUrl}/${messageId}`, request);
  }

  /**
   * 开始助理消息的生成过程
   */
  startGeneration(messageId: string): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(
        `${this.apiUrl}/${messageId}/generate`,
        {},
    );
  }

  /**
   * 删除消息
   */
  delete(messageId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${messageId}`);
  }

  /**
   * 点赞消息
   */
  like(messageId: string): Observable<ChatMessage> {
    return this.http.post<ChatMessage>(`${this.apiUrl}/${messageId}/like`, {});
  }

  /**
   * 点踩消息
   */
  dislike(messageId: string): Observable<ChatMessage> {
    return this.http.post<ChatMessage>(
        `${this.apiUrl}/${messageId}/dislike`,
        {},
    );
  }

  /**
   * 添加消息反馈
   */
  addFeedback(
      messageId: string,
      feedback: ChatMessageFeedback,
  ): Observable<ChatMessage> {
    return this.http.post<ChatMessage>(
        `${this.apiUrl}/${messageId}/feedback`,
        feedback,
    );
  }

  /**
   * 重新生成消息
   * @param messageId 助理消息ID
   * @param sessionId 会话ID
   * @returns 包含新助理消息的Observable
   */
  regenerate(messageId: string, sessionId: string): Observable<{ assistantMessage: ChatMessage }> {
    return this.http.post<{ assistantMessage: ChatMessage }>(`${this.apiUrl}/regenerate`, {
      messageId,
      sessionId,
    });
  }


  /**
   * 获取消息的事件流
   * 使用SSE (Server-Sent Events)实现实时更新
   * 只包含与指定消息相关的事件
   * @param messageId 消息ID
   */
  getEventStream(messageId: string): Observable<ChatMessageEvent> {
    const token = this.tokenHolder.getToken();
    if (!token) {
      throw 'Token 不能为空！';
    }
    return new Observable<ChatMessageEvent>((observer) => {
      const url = `${
          environment.apiBaseUrl
      }/messages/${messageId}/events?token=${encodeURIComponent(token)}`;
      const eventSource = new EventSource(url);

      // 处理消息生成内容事件
      eventSource.addEventListener('streaming-content', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'streaming-content',
            text: data.text,
            index: data.index,
          });
        } catch (error) {
          console.error('解析流式内容事件失败', error);
        }
      });

      // 处理消息生成阶段事件
      eventSource.addEventListener('generation-steps', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'generation-steps',
            steps: data.steps,
          });
        } catch (error) {
          console.error('解析生成阶段数据失败', error);
        }
      });

      // 处理消息生成完成事件
      eventSource.addEventListener('generation-complete', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'generation-complete',
            message: data.message,
          });
          // 生成完成后自动关闭事件流
          eventSource.close();
          observer.complete();
        } catch (error) {
          console.error('解析生成完成事件失败', error);
        }
      });

      // 处理领域适配事件
      eventSource.addEventListener('domain-adaptation', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'domain-adaptation',
            status: data.status,
            result: data.result,
            message: data.message,
          });
        } catch (error) {
          console.error('解析领域适配事件失败', error);
        }
      });

      // 处理执行计划事件
      eventSource.addEventListener('execution-plan', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'execution-plan',
            executionContext: data.executionContext,
          });
        } catch (error) {
          console.error('解析执行计划事件失败', error);
        }
      });

      // 处理执行更新事件
      eventSource.addEventListener('execution-update', (event) => {
        try {
          const data = JSON.parse(event.data);
          observer.next({
            messageId: data.messageId,
            type: 'execution-update',
            executionContext: data.executionContext,
            eventType: data.eventType,
            currentStep: data.currentStep,
            message: data.message,
          });
        } catch (error) {
          console.error('解析执行更新事件失败', error);
        }
      });



      // 处理错误
      eventSource.onerror = (error) => {
        console.error('SSE错误', error);
        eventSource.close();
        observer.error(error);
      };

      // 清理函数
      return () => {
        eventSource.close();
      };
    });
  }
}
