package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.SettingsResponse
import biz.zhizuo.ai.assistant.dto.UpdateSettingsRequest
import biz.zhizuo.ai.assistant.security.UserPrincipal
import biz.zhizuo.ai.assistant.service.SettingsService
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

/**
 * 设置控制器
 */
@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = ["*"])
class SettingsController(
    private val settingsService: SettingsService
) {

    /**
     * 获取用户设置
     */
    @GetMapping
    fun getSettings(
        @AuthenticationPrincipal userPrincipal: UserPrincipal
    ): ResponseEntity<SettingsResponse> {
        val response = settingsService.getSettings(userPrincipal.id)
        return ResponseEntity.ok(response)
    }

    /**
     * 更新用户设置
     */
    @PatchMapping
    fun updateSettings(
        @AuthenticationPrincipal userPrincipal: UserPrincipal,
        @Valid @RequestBody request: UpdateSettingsRequest
    ): ResponseEntity<SettingsResponse> {
        val response = settingsService.updateSettings(userPrincipal.id, request)
        return ResponseEntity.ok(response)
    }
}
