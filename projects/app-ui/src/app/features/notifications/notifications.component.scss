:host {
  display: block;
}

.messages-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-8) 0;
}

.tab-badge {
  background-color: var(--accent-color);
  color: var(--md-sys-color-primary);
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  margin-left: var(--md-sys-spacing-1);
}

.tab-content {
  padding: var(--md-sys-spacing-4) 0;
}

.message-actions {
  display: flex;
  margin-bottom: var(--md-sys-spacing-4);

  button {
    margin-left: var(--md-sys-spacing-2);
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);

  mat-card {
    transition: background-color var(--md-sys-motion-duration-short2);

    &.unread {
      background-color: rgba(25, 118, 210, 0.05);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background-color: var(--md-sys-color-primary);
      }
    }
  }

  .message-item {
    display: flex;
    align-items: flex-start;
  }

  .message-icon {
    margin-right: var(--md-sys-spacing-4);

    &.system {
      background-color: #2196f3;
    }

    &.update {
      background-color: #ff9800;
    }

    &.message {
      background-color: #4caf50;
    }

    &.activity {
      background-color: #9c27b0;
    }
  }

  .message-avatar {
    margin-right: var(--md-sys-spacing-4);

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .message-content {
    flex: 1;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--md-sys-spacing-1);

      h3 {
        margin: 0;
        font-size: var(--md-sys-typescale-body-medium-size);
        font-weight: 500;
      }

      .message-time {
        color: var(--md-sys-color-on-surface-variant);
        font-size: var(--md-sys-typescale-label-large-size);
      }
    }

    p {
      margin: 0;
      color: var(--md-sys-color-on-surface-variant);
    }
  }
}

@media (max-width: 599px) {
  .message-actions {
    flex-direction: column;
    align-items: stretch;

    button {
      margin: 0 0 var(--md-sys-spacing-1) 0;
    }
  }

  .message-item {
    .message-header {
      flex-direction: column;
      align-items: flex-start;

      .message-time {
        margin-top: var(--md-sys-spacing-1);
      }
    }
  }
}
