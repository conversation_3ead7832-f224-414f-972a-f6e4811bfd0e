package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.ChatSessionDto
import biz.zhizuo.ai.assistant.dto.CreateSessionRequest
import biz.zhizuo.ai.assistant.dto.UpdateSessionRequest
import biz.zhizuo.ai.assistant.entity.ChatSession
import biz.zhizuo.ai.assistant.repository.ChatSessionRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 聊天会话服务
 */
@Service
@Transactional
class ChatSessionService(
    private val userService: UserService,
    private val chatSessionRepository: ChatSessionRepository,
) {

    /**
     * 获取用户的所有会话
     */
    @Transactional(readOnly = true)
    fun getUserSessions(userId: String): List<ChatSessionDto> {
        return chatSessionRepository.findByUserIdOrderByUpdatedAtDesc(userId)
            .map { it.toDto() }
    }

    /**
     * 根据ID获取会话
     */
    @Transactional(readOnly = true)
    fun getSessionById(sessionId: String, userId: String): ChatSession {
        return chatSessionRepository.findByIdAndUserId(sessionId, userId)
            ?: throw ChatSessionNotFoundException("Chat session not found with id: $sessionId for user: $userId")
    }

    /**
     * 创建新会话
     */
    fun createSession(request: CreateSessionRequest, userId: String): ChatSessionDto {
        val user = userService.getUserById(userId)
        val session = ChatSession(
            user = user,
            name = request.name,
            pinned = false
        )
        val savedSession = chatSessionRepository.save(session)
        return savedSession.toDto()
    }

    /**
     * 更新会话
     */
    fun updateSession(sessionId: String, request: UpdateSessionRequest, userId: String): ChatSessionDto {
        val existingSession = getSessionById(sessionId, userId)

        val updatedSession = existingSession.copy(
            name = request.name ?: existingSession.name,
            pinned = request.pinned ?: existingSession.pinned
        )

        val savedSession = chatSessionRepository.save(updatedSession)
        return savedSession.toDto()
    }

    /**
     * 删除会话
     */
    fun deleteSession(sessionId: String, userId: String): Boolean {
        val session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
            ?: return false

        chatSessionRepository.delete(session)
        return true
    }

    /**
     * 更新会话的最后消息时间
     */
    fun updateLastMessageTime(sessionId: String, lastMessageAt: LocalDateTime) {
        val session = chatSessionRepository.findByIdOrNull(sessionId) ?: throw ChatSessionNotFoundException(sessionId)
        val updatedSession = session.copy(lastMessageAt = lastMessageAt)
        chatSessionRepository.save(updatedSession)
    }

    /**
     * 获取用户会话数量
     */
    @Transactional(readOnly = true)
    fun getUserSessionCount(userId: String): Long {
        return chatSessionRepository.countByUserId(userId)
    }

    /**
     * 删除用户的所有会话
     */
    fun deleteAllUserSessions(userId: String) {
        chatSessionRepository.deleteByUserId(userId)
    }

    /**
     * 实体转DTO
     */
    private fun ChatSession.toDto(): ChatSessionDto {
        return ChatSessionDto(
            id = this.id,
            name = this.name,
            userId = this.user.id!!,
            pinned = this.pinned,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            lastMessageAt = this.lastMessageAt
        )
    }

    fun getSessionDtoById(messageId: String, userId: String): ChatSessionDto {
        return getSessionById(messageId, userId).toDto()
    }
}
